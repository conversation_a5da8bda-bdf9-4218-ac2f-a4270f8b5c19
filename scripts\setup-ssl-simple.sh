#!/bin/bash

# Simple SSL Setup Script for a2t.io.vn
# Only gets SSL certificate (assumes Nginx already configured)

set -e

DOMAIN="a2t.io.vn"
EMAIL="<EMAIL>"  # Change this to your email

echo "🔒 Setting up SSL Certificate for $DOMAIN"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run this script as root (use sudo)"
    exit 1
fi

# Update system
echo "📦 Updating system packages..."
apt update

# Install Certbot if not installed
if ! command -v certbot &> /dev/null; then
    echo "📦 Installing Certbot..."
    apt install snapd -y
    snap install core; snap refresh core
    snap install --classic certbot
    ln -sf /snap/bin/certbot /usr/bin/certbot
fi

# Stop nginx temporarily to get certificate
echo "⏸️ Stopping Nginx temporarily..."
systemctl stop nginx

# Get SSL certificate using standalone mode
echo "🔒 Obtaining SSL certificate..."
certbot certonly --standalone \
    -d $DOMAIN \
    -d www.$DOMAIN \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    --non-interactive

# Create simple nginx SSL proxy configuration
echo "⚙️ Creating SSL proxy configuration for Nginx..."
cat > /etc/nginx/sites-available/$DOMAIN << 'EOF'
server {
    listen 80;
    server_name a2t.io.vn www.a2t.io.vn;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name a2t.io.vn www.a2t.io.vn;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/a2t.io.vn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/a2t.io.vn/privkey.pem;

    # SSL Security
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Proxy everything to Docker Nginx container
    # Docker Nginx container handles all routing internally
    location / {
        proxy_pass http://localhost:80;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
echo "🧪 Testing Nginx configuration..."
nginx -t

# Start Nginx
echo "🚀 Starting Nginx..."
systemctl start nginx
systemctl enable nginx

# Set up auto-renewal
echo "⏰ Setting up auto-renewal..."
systemctl enable snap.certbot.renew.timer

# Test auto-renewal
echo "🧪 Testing auto-renewal..."
certbot renew --dry-run

echo "✅ SSL setup completed successfully!"
echo "🌐 Your site is now available at: https://$DOMAIN"
echo "📊 API documentation: https://$DOMAIN/api-docs"
echo "🔄 SSL certificate will auto-renew every 90 days"

# Display certificate info
echo "📋 Certificate information:"
certbot certificates

# Show next steps
echo ""
echo "🎯 Next steps:"
echo "1. Update your .env file:"
echo "   NEXT_PUBLIC_API_URL=https://a2t.io.vn/api"
echo "   ALLOWED_ORIGINS=https://a2t.io.vn,http://a2t.io.vn"
echo ""
echo "2. Restart your Docker containers:"
echo "   docker-compose restart"
