{"F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\app.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\app.js", "statementMap": {"0": {"start": {"line": 2, "column": 16}, "end": {"line": 2, "column": 34}}, "1": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 28}}, "2": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 40}}, "3": {"start": {"line": 5, "column": 19}, "end": {"line": 5, "column": 48}}, "4": {"start": {"line": 6, "column": 19}, "end": {"line": 6, "column": 48}}, "5": {"start": {"line": 7, "column": 21}, "end": {"line": 7, "column": 52}}, "6": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 58}}, "7": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 56}}, "8": {"start": {"line": 10, "column": 22}, "end": {"line": 10, "column": 54}}, "9": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 54}}, "10": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 28}}, "11": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 21}}, "12": {"start": {"line": 17, "column": 26}, "end": {"line": 31, "column": 1}}, "13": {"start": {"line": 18, "column": 18}, "end": {"line": 18, "column": 43}}, "14": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, "15": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 41}}, "16": {"start": {"line": 26, "column": 2}, "end": {"line": 28, "column": 3}}, "17": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 60}}, "18": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 17}}, "19": {"start": {"line": 33, "column": 20}, "end": {"line": 49, "column": 1}}, "20": {"start": {"line": 35, "column": 27}, "end": {"line": 35, "column": 46}}, "21": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 45}}, "22": {"start": {"line": 37, "column": 17}, "end": {"line": 37, "column": 45}}, "23": {"start": {"line": 39, "column": 4}, "end": {"line": 45, "column": 5}}, "24": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 27}}, "25": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 50}}, "26": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 54}}, "27": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 49}}, "28": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 27}}, "29": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 24}}, "30": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 88}}, "31": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 51}}, "32": {"start": {"line": 60, "column": 0}, "end": {"line": 67, "column": 3}}, "33": {"start": {"line": 61, "column": 2}, "end": {"line": 66, "column": 5}}, "34": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 34}}, "35": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 33}}, "36": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 45}}, "37": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 37}}, "38": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 41}}, "39": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 40}}, "40": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 39}}, "41": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 21}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 17, "column": 26}, "end": {"line": 17, "column": 27}}, "loc": {"start": {"line": 17, "column": 32}, "end": {"line": 31, "column": 1}}, "line": 17}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 34, "column": 10}, "end": {"line": 34, "column": 11}}, "loc": {"start": {"line": 34, "column": 38}, "end": {"line": 46, "column": 3}}, "line": 34}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 60, "column": 23}, "end": {"line": 60, "column": 24}}, "loc": {"start": {"line": 60, "column": 37}, "end": {"line": 67, "column": 1}}, "line": 60}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, "type": "if", "locations": [{"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, {"start": {}, "end": {}}], "line": 21}, "1": {"loc": {"start": {"line": 26, "column": 2}, "end": {"line": 28, "column": 3}}, "type": "if", "locations": [{"start": {"line": 26, "column": 2}, "end": {"line": 28, "column": 3}}, {"start": {}, "end": {}}], "line": 26}, "2": {"loc": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 45}}, "type": "if", "locations": [{"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 45}}, {"start": {}, "end": {}}], "line": 37}, "3": {"loc": {"start": {"line": 39, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 39, "column": 4}, "end": {"line": 45, "column": 5}}, {"start": {"line": 41, "column": 11}, "end": {"line": 45, "column": 5}}], "line": 39}, "4": {"loc": {"start": {"line": 65, "column": 17}, "end": {"line": 65, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 65, "column": 17}, "end": {"line": 65, "column": 37}}, {"start": {"line": 65, "column": 41}, "end": {"line": 65, "column": 54}}], "line": 65}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\genjwt.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\genjwt.js", "statementMap": {"0": {"start": {"line": 1, "column": 15}, "end": {"line": 1, "column": 32}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 48}}, "2": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 31}}, "3": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 35}}}, "fnMap": {"0": {"name": "generateSecret", "decl": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 23}}, "loc": {"start": {"line": 3, "column": 26}, "end": {"line": 5, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\api\\swagger.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\api\\swagger.js", "statementMap": {"0": {"start": {"line": 1, "column": 21}, "end": {"line": 1, "column": 45}}, "1": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 47}}, "2": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 28}}, "3": {"start": {"line": 5, "column": 16}, "end": {"line": 37, "column": 1}}, "4": {"start": {"line": 39, "column": 14}, "end": {"line": 39, "column": 35}}, "5": {"start": {"line": 41, "column": 0}, "end": {"line": 48, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\allcodeController.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\allcodeController.js", "statementMap": {"0": {"start": {"line": 1, "column": 20}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 4, "column": 26}, "end": {"line": 30, "column": 1}}, "2": {"start": {"line": 5, "column": 4}, "end": {"line": 29, "column": 5}}, "3": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 34}}, "4": {"start": {"line": 7, "column": 8}, "end": {"line": 11, "column": 9}}, "5": {"start": {"line": 8, "column": 12}, "end": {"line": 10, "column": 15}}, "6": {"start": {"line": 13, "column": 25}, "end": {"line": 17, "column": 10}}, "7": {"start": {"line": 19, "column": 8}, "end": {"line": 22, "column": 11}}, "8": {"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 60}}, "9": {"start": {"line": 25, "column": 8}, "end": {"line": 28, "column": 11}}, "10": {"start": {"line": 33, "column": 20}, "end": {"line": 52, "column": 1}}, "11": {"start": {"line": 34, "column": 4}, "end": {"line": 51, "column": 5}}, "12": {"start": {"line": 35, "column": 22}, "end": {"line": 39, "column": 10}}, "13": {"start": {"line": 41, "column": 8}, "end": {"line": 44, "column": 11}}, "14": {"start": {"line": 43, "column": 36}, "end": {"line": 43, "column": 45}}, "15": {"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": 54}}, "16": {"start": {"line": 47, "column": 8}, "end": {"line": 50, "column": 11}}, "17": {"start": {"line": 55, "column": 21}, "end": {"line": 80, "column": 1}}, "18": {"start": {"line": 56, "column": 4}, "end": {"line": 79, "column": 5}}, "19": {"start": {"line": 57, "column": 27}, "end": {"line": 57, "column": 37}}, "20": {"start": {"line": 58, "column": 21}, "end": {"line": 61, "column": 10}}, "21": {"start": {"line": 63, "column": 8}, "end": {"line": 67, "column": 9}}, "22": {"start": {"line": 64, "column": 12}, "end": {"line": 66, "column": 15}}, "23": {"start": {"line": 69, "column": 8}, "end": {"line": 72, "column": 11}}, "24": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": 55}}, "25": {"start": {"line": 75, "column": 8}, "end": {"line": 78, "column": 11}}, "26": {"start": {"line": 83, "column": 19}, "end": {"line": 123, "column": 1}}, "27": {"start": {"line": 84, "column": 4}, "end": {"line": 122, "column": 5}}, "28": {"start": {"line": 85, "column": 51}, "end": {"line": 85, "column": 59}}, "29": {"start": {"line": 88, "column": 8}, "end": {"line": 92, "column": 9}}, "30": {"start": {"line": 89, "column": 12}, "end": {"line": 91, "column": 15}}, "31": {"start": {"line": 95, "column": 29}, "end": {"line": 97, "column": 10}}, "32": {"start": {"line": 99, "column": 8}, "end": {"line": 103, "column": 9}}, "33": {"start": {"line": 100, "column": 12}, "end": {"line": 102, "column": 15}}, "34": {"start": {"line": 105, "column": 24}, "end": {"line": 110, "column": 10}}, "35": {"start": {"line": 112, "column": 8}, "end": {"line": 115, "column": 11}}, "36": {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 53}}, "37": {"start": {"line": 118, "column": 8}, "end": {"line": 121, "column": 11}}, "38": {"start": {"line": 126, "column": 19}, "end": {"line": 158, "column": 1}}, "39": {"start": {"line": 127, "column": 4}, "end": {"line": 157, "column": 5}}, "40": {"start": {"line": 128, "column": 27}, "end": {"line": 128, "column": 37}}, "41": {"start": {"line": 129, "column": 37}, "end": {"line": 129, "column": 45}}, "42": {"start": {"line": 131, "column": 21}, "end": {"line": 133, "column": 10}}, "43": {"start": {"line": 135, "column": 8}, "end": {"line": 139, "column": 9}}, "44": {"start": {"line": 136, "column": 12}, "end": {"line": 138, "column": 15}}, "45": {"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 44}}, "46": {"start": {"line": 142, "column": 21}, "end": {"line": 142, "column": 44}}, "47": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 44}}, "48": {"start": {"line": 143, "column": 21}, "end": {"line": 143, "column": 44}}, "49": {"start": {"line": 145, "column": 8}, "end": {"line": 145, "column": 26}}, "50": {"start": {"line": 147, "column": 8}, "end": {"line": 150, "column": 11}}, "51": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 53}}, "52": {"start": {"line": 153, "column": 8}, "end": {"line": 156, "column": 11}}, "53": {"start": {"line": 161, "column": 19}, "end": {"line": 187, "column": 1}}, "54": {"start": {"line": 162, "column": 4}, "end": {"line": 186, "column": 5}}, "55": {"start": {"line": 163, "column": 27}, "end": {"line": 163, "column": 37}}, "56": {"start": {"line": 165, "column": 21}, "end": {"line": 167, "column": 10}}, "57": {"start": {"line": 169, "column": 8}, "end": {"line": 173, "column": 9}}, "58": {"start": {"line": 170, "column": 12}, "end": {"line": 172, "column": 15}}, "59": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 29}}, "60": {"start": {"line": 177, "column": 8}, "end": {"line": 179, "column": 11}}, "61": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 53}}, "62": {"start": {"line": 182, "column": 8}, "end": {"line": 185, "column": 11}}, "63": {"start": {"line": 189, "column": 0}, "end": {"line": 196, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 26}, "end": {"line": 4, "column": 27}}, "loc": {"start": {"line": 4, "column": 46}, "end": {"line": 30, "column": 1}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 33, "column": 20}, "end": {"line": 33, "column": 21}}, "loc": {"start": {"line": 33, "column": 40}, "end": {"line": 52, "column": 1}}, "line": 33}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 43, "column": 28}, "end": {"line": 43, "column": 29}}, "loc": {"start": {"line": 43, "column": 36}, "end": {"line": 43, "column": 45}}, "line": 43}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 55, "column": 21}, "end": {"line": 55, "column": 22}}, "loc": {"start": {"line": 55, "column": 41}, "end": {"line": 80, "column": 1}}, "line": 55}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 83, "column": 19}, "end": {"line": 83, "column": 20}}, "loc": {"start": {"line": 83, "column": 39}, "end": {"line": 123, "column": 1}}, "line": 83}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 126, "column": 19}, "end": {"line": 126, "column": 20}}, "loc": {"start": {"line": 126, "column": 39}, "end": {"line": 158, "column": 1}}, "line": 126}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 161, "column": 19}, "end": {"line": 161, "column": 20}}, "loc": {"start": {"line": 161, "column": 39}, "end": {"line": 187, "column": 1}}, "line": 161}}, "branchMap": {"0": {"loc": {"start": {"line": 7, "column": 8}, "end": {"line": 11, "column": 9}}, "type": "if", "locations": [{"start": {"line": 7, "column": 8}, "end": {"line": 11, "column": 9}}, {"start": {}, "end": {}}], "line": 7}, "1": {"loc": {"start": {"line": 63, "column": 8}, "end": {"line": 67, "column": 9}}, "type": "if", "locations": [{"start": {"line": 63, "column": 8}, "end": {"line": 67, "column": 9}}, {"start": {}, "end": {}}], "line": 63}, "2": {"loc": {"start": {"line": 88, "column": 8}, "end": {"line": 92, "column": 9}}, "type": "if", "locations": [{"start": {"line": 88, "column": 8}, "end": {"line": 92, "column": 9}}, {"start": {}, "end": {}}], "line": 88}, "3": {"loc": {"start": {"line": 88, "column": 12}, "end": {"line": 88, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 88, "column": 12}, "end": {"line": 88, "column": 19}}, {"start": {"line": 88, "column": 23}, "end": {"line": 88, "column": 28}}, {"start": {"line": 88, "column": 32}, "end": {"line": 88, "column": 40}}, {"start": {"line": 88, "column": 44}, "end": {"line": 88, "column": 52}}], "line": 88}, "4": {"loc": {"start": {"line": 99, "column": 8}, "end": {"line": 103, "column": 9}}, "type": "if", "locations": [{"start": {"line": 99, "column": 8}, "end": {"line": 103, "column": 9}}, {"start": {}, "end": {}}], "line": 99}, "5": {"loc": {"start": {"line": 135, "column": 8}, "end": {"line": 139, "column": 9}}, "type": "if", "locations": [{"start": {"line": 135, "column": 8}, "end": {"line": 139, "column": 9}}, {"start": {}, "end": {}}], "line": 135}, "6": {"loc": {"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 44}}, "type": "if", "locations": [{"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 44}}, {"start": {}, "end": {}}], "line": 142}, "7": {"loc": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 44}}, "type": "if", "locations": [{"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 44}}, {"start": {}, "end": {}}], "line": 143}, "8": {"loc": {"start": {"line": 169, "column": 8}, "end": {"line": 173, "column": 9}}, "type": "if", "locations": [{"start": {"line": 169, "column": 8}, "end": {"line": 173, "column": 9}}, {"start": {}, "end": {}}], "line": 169}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0, 0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\authController.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\authController.js", "statementMap": {"0": {"start": {"line": 2, "column": 13}, "end": {"line": 2, "column": 38}}, "1": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 34}}, "2": {"start": {"line": 4, "column": 12}, "end": {"line": 4, "column": 35}}, "3": {"start": {"line": 5, "column": 26}, "end": {"line": 5, "column": 48}}, "4": {"start": {"line": 10, "column": 18}, "end": {"line": 48, "column": 1}}, "5": {"start": {"line": 11, "column": 4}, "end": {"line": 47, "column": 5}}, "6": {"start": {"line": 12, "column": 36}, "end": {"line": 12, "column": 44}}, "7": {"start": {"line": 15, "column": 8}, "end": {"line": 17, "column": 9}}, "8": {"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": 88}}, "9": {"start": {"line": 19, "column": 21}, "end": {"line": 19, "column": 61}}, "10": {"start": {"line": 21, "column": 8}, "end": {"line": 43, "column": 9}}, "11": {"start": {"line": 23, "column": 28}, "end": {"line": 29, "column": 13}}, "12": {"start": {"line": 30, "column": 26}, "end": {"line": 30, "column": 88}}, "13": {"start": {"line": 32, "column": 12}, "end": {"line": 40, "column": 15}}, "14": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 82}}, "15": {"start": {"line": 45, "column": 8}, "end": {"line": 45, "column": 29}}, "16": {"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": 78}}, "17": {"start": {"line": 50, "column": 23}, "end": {"line": 64, "column": 1}}, "18": {"start": {"line": 51, "column": 4}, "end": {"line": 63, "column": 5}}, "19": {"start": {"line": 52, "column": 23}, "end": {"line": 52, "column": 38}}, "20": {"start": {"line": 54, "column": 21}, "end": {"line": 54, "column": 48}}, "21": {"start": {"line": 56, "column": 8}, "end": {"line": 58, "column": 9}}, "22": {"start": {"line": 57, "column": 12}, "end": {"line": 57, "column": 82}}, "23": {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 23}}, "24": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 78}}, "25": {"start": {"line": 66, "column": 0}, "end": {"line": 69, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 18}, "end": {"line": 10, "column": 19}}, "loc": {"start": {"line": 10, "column": 38}, "end": {"line": 48, "column": 1}}, "line": 10}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 50, "column": 23}, "end": {"line": 50, "column": 24}}, "loc": {"start": {"line": 50, "column": 43}, "end": {"line": 64, "column": 1}}, "line": 50}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 8}, "end": {"line": 17, "column": 9}}, "type": "if", "locations": [{"start": {"line": 15, "column": 8}, "end": {"line": 17, "column": 9}}, {"start": {}, "end": {}}], "line": 15}, "1": {"loc": {"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 18}}, {"start": {"line": 15, "column": 22}, "end": {"line": 15, "column": 31}}], "line": 15}, "2": {"loc": {"start": {"line": 21, "column": 8}, "end": {"line": 43, "column": 9}}, "type": "if", "locations": [{"start": {"line": 21, "column": 8}, "end": {"line": 43, "column": 9}}, {"start": {"line": 41, "column": 15}, "end": {"line": 43, "column": 9}}], "line": 21}, "3": {"loc": {"start": {"line": 21, "column": 12}, "end": {"line": 21, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 12}, "end": {"line": 21, "column": 16}}, {"start": {"line": 21, "column": 21}, "end": {"line": 21, "column": 66}}], "line": 21}, "4": {"loc": {"start": {"line": 56, "column": 8}, "end": {"line": 58, "column": 9}}, "type": "if", "locations": [{"start": {"line": 56, "column": 8}, "end": {"line": 58, "column": 9}}, {"start": {}, "end": {}}], "line": 56}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\bookingController.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\bookingController.js", "statementMap": {"0": {"start": {"line": 1, "column": 11}, "end": {"line": 1, "column": 31}}, "1": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 35}}, "2": {"start": {"line": 6, "column": 0}, "end": {"line": 26, "column": 2}}, "3": {"start": {"line": 7, "column": 4}, "end": {"line": 25, "column": 5}}, "4": {"start": {"line": 8, "column": 66}, "end": {"line": 8, "column": 74}}, "5": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 52}}, "6": {"start": {"line": 13, "column": 27}, "end": {"line": 20, "column": 10}}, "7": {"start": {"line": 22, "column": 8}, "end": {"line": 22, "column": 66}}, "8": {"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 79}}, "9": {"start": {"line": 29, "column": 0}, "end": {"line": 92, "column": 2}}, "10": {"start": {"line": 30, "column": 4}, "end": {"line": 91, "column": 5}}, "11": {"start": {"line": 31, "column": 29}, "end": {"line": 31, "column": 39}}, "12": {"start": {"line": 33, "column": 25}, "end": {"line": 80, "column": 10}}, "13": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 64}}, "14": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 62}}, "15": {"start": {"line": 85, "column": 8}, "end": {"line": 90, "column": 11}}, "16": {"start": {"line": 95, "column": 0}, "end": {"line": 171, "column": 2}}, "17": {"start": {"line": 96, "column": 4}, "end": {"line": 170, "column": 5}}, "18": {"start": {"line": 97, "column": 26}, "end": {"line": 97, "column": 60}}, "19": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 56}}, "20": {"start": {"line": 100, "column": 8}, "end": {"line": 102, "column": 9}}, "21": {"start": {"line": 101, "column": 12}, "end": {"line": 101, "column": 96}}, "22": {"start": {"line": 104, "column": 25}, "end": {"line": 151, "column": 10}}, "23": {"start": {"line": 153, "column": 8}, "end": {"line": 159, "column": 9}}, "24": {"start": {"line": 154, "column": 12}, "end": {"line": 158, "column": 15}}, "25": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 64}}, "26": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 64}}, "27": {"start": {"line": 164, "column": 8}, "end": {"line": 169, "column": 11}}, "28": {"start": {"line": 174, "column": 0}, "end": {"line": 191, "column": 2}}, "29": {"start": {"line": 175, "column": 4}, "end": {"line": 190, "column": 5}}, "30": {"start": {"line": 176, "column": 23}, "end": {"line": 176, "column": 33}}, "31": {"start": {"line": 178, "column": 24}, "end": {"line": 178, "column": 53}}, "32": {"start": {"line": 179, "column": 8}, "end": {"line": 181, "column": 9}}, "33": {"start": {"line": 180, "column": 12}, "end": {"line": 180, "column": 97}}, "34": {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 32}}, "35": {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 29}}, "36": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 95}}, "37": {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 79}}, "38": {"start": {"line": 194, "column": 0}, "end": {"line": 210, "column": 2}}, "39": {"start": {"line": 195, "column": 4}, "end": {"line": 209, "column": 5}}, "40": {"start": {"line": 196, "column": 27}, "end": {"line": 196, "column": 37}}, "41": {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 53}}, "42": {"start": {"line": 199, "column": 24}, "end": {"line": 204, "column": 10}}, "43": {"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 101}}, "44": {"start": {"line": 208, "column": 8}, "end": {"line": 208, "column": 79}}, "45": {"start": {"line": 213, "column": 0}, "end": {"line": 272, "column": 2}}, "46": {"start": {"line": 214, "column": 4}, "end": {"line": 271, "column": 5}}, "47": {"start": {"line": 215, "column": 23}, "end": {"line": 215, "column": 33}}, "48": {"start": {"line": 216, "column": 8}, "end": {"line": 218, "column": 9}}, "49": {"start": {"line": 217, "column": 12}, "end": {"line": 217, "column": 91}}, "50": {"start": {"line": 219, "column": 24}, "end": {"line": 264, "column": 10}}, "51": {"start": {"line": 265, "column": 8}, "end": {"line": 267, "column": 9}}, "52": {"start": {"line": 266, "column": 12}, "end": {"line": 266, "column": 97}}, "53": {"start": {"line": 268, "column": 8}, "end": {"line": 268, "column": 63}}, "54": {"start": {"line": 270, "column": 8}, "end": {"line": 270, "column": 94}}, "55": {"start": {"line": 274, "column": 0}, "end": {"line": 310, "column": 2}}, "56": {"start": {"line": 275, "column": 4}, "end": {"line": 309, "column": 5}}, "57": {"start": {"line": 276, "column": 23}, "end": {"line": 276, "column": 33}}, "58": {"start": {"line": 277, "column": 29}, "end": {"line": 277, "column": 37}}, "59": {"start": {"line": 278, "column": 24}, "end": {"line": 278, "column": 53}}, "60": {"start": {"line": 279, "column": 8}, "end": {"line": 281, "column": 9}}, "61": {"start": {"line": 280, "column": 12}, "end": {"line": 280, "column": 97}}, "62": {"start": {"line": 282, "column": 26}, "end": {"line": 282, "column": 42}}, "63": {"start": {"line": 283, "column": 8}, "end": {"line": 283, "column": 36}}, "64": {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 29}}, "65": {"start": {"line": 287, "column": 25}, "end": {"line": 293, "column": 10}}, "66": {"start": {"line": 296, "column": 8}, "end": {"line": 299, "column": 9}}, "67": {"start": {"line": 297, "column": 12}, "end": {"line": 297, "column": 40}}, "68": {"start": {"line": 298, "column": 12}, "end": {"line": 298, "column": 34}}, "69": {"start": {"line": 301, "column": 8}, "end": {"line": 304, "column": 9}}, "70": {"start": {"line": 302, "column": 12}, "end": {"line": 302, "column": 40}}, "71": {"start": {"line": 303, "column": 12}, "end": {"line": 303, "column": 34}}, "72": {"start": {"line": 306, "column": 8}, "end": {"line": 306, "column": 106}}, "73": {"start": {"line": 308, "column": 8}, "end": {"line": 308, "column": 79}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 25}}, "loc": {"start": {"line": 6, "column": 44}, "end": {"line": 26, "column": 1}}, "line": 6}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 29, "column": 30}, "end": {"line": 29, "column": 31}}, "loc": {"start": {"line": 29, "column": 50}, "end": {"line": 92, "column": 1}}, "line": 29}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 95, "column": 31}, "end": {"line": 95, "column": 32}}, "loc": {"start": {"line": 95, "column": 51}, "end": {"line": 171, "column": 1}}, "line": 95}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 174, "column": 24}, "end": {"line": 174, "column": 25}}, "loc": {"start": {"line": 174, "column": 44}, "end": {"line": 191, "column": 1}}, "line": 174}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 194, "column": 37}, "end": {"line": 194, "column": 38}}, "loc": {"start": {"line": 194, "column": 57}, "end": {"line": 210, "column": 1}}, "line": 194}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 213, "column": 25}, "end": {"line": 213, "column": 26}}, "loc": {"start": {"line": 213, "column": 45}, "end": {"line": 272, "column": 1}}, "line": 213}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 274, "column": 30}, "end": {"line": 274, "column": 31}}, "loc": {"start": {"line": 274, "column": 50}, "end": {"line": 310, "column": 1}}, "line": 274}}, "branchMap": {"0": {"loc": {"start": {"line": 89, "column": 19}, "end": {"line": 89, "column": 83}}, "type": "cond-expr", "locations": [{"start": {"line": 89, "column": 60}, "end": {"line": 89, "column": 71}}, {"start": {"line": 89, "column": 74}, "end": {"line": 89, "column": 83}}], "line": 89}, "1": {"loc": {"start": {"line": 100, "column": 8}, "end": {"line": 102, "column": 9}}, "type": "if", "locations": [{"start": {"line": 100, "column": 8}, "end": {"line": 102, "column": 9}}, {"start": {}, "end": {}}], "line": 100}, "2": {"loc": {"start": {"line": 100, "column": 12}, "end": {"line": 100, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 12}, "end": {"line": 100, "column": 28}}, {"start": {"line": 100, "column": 32}, "end": {"line": 100, "column": 46}}], "line": 100}, "3": {"loc": {"start": {"line": 153, "column": 8}, "end": {"line": 159, "column": 9}}, "type": "if", "locations": [{"start": {"line": 153, "column": 8}, "end": {"line": 159, "column": 9}}, {"start": {}, "end": {}}], "line": 153}, "4": {"loc": {"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 21}}, {"start": {"line": 153, "column": 25}, "end": {"line": 153, "column": 46}}], "line": 153}, "5": {"loc": {"start": {"line": 168, "column": 19}, "end": {"line": 168, "column": 83}}, "type": "cond-expr", "locations": [{"start": {"line": 168, "column": 60}, "end": {"line": 168, "column": 71}}, {"start": {"line": 168, "column": 74}, "end": {"line": 168, "column": 83}}], "line": 168}, "6": {"loc": {"start": {"line": 179, "column": 8}, "end": {"line": 181, "column": 9}}, "type": "if", "locations": [{"start": {"line": 179, "column": 8}, "end": {"line": 181, "column": 9}}, {"start": {}, "end": {}}], "line": 179}, "7": {"loc": {"start": {"line": 216, "column": 8}, "end": {"line": 218, "column": 9}}, "type": "if", "locations": [{"start": {"line": 216, "column": 8}, "end": {"line": 218, "column": 9}}, {"start": {}, "end": {}}], "line": 216}, "8": {"loc": {"start": {"line": 265, "column": 8}, "end": {"line": 267, "column": 9}}, "type": "if", "locations": [{"start": {"line": 265, "column": 8}, "end": {"line": 267, "column": 9}}, {"start": {}, "end": {}}], "line": 265}, "9": {"loc": {"start": {"line": 279, "column": 8}, "end": {"line": 281, "column": 9}}, "type": "if", "locations": [{"start": {"line": 279, "column": 8}, "end": {"line": 281, "column": 9}}, {"start": {}, "end": {}}], "line": 279}, "10": {"loc": {"start": {"line": 296, "column": 8}, "end": {"line": 299, "column": 9}}, "type": "if", "locations": [{"start": {"line": 296, "column": 8}, "end": {"line": 299, "column": 9}}, {"start": {}, "end": {}}], "line": 296}, "11": {"loc": {"start": {"line": 296, "column": 12}, "end": {"line": 296, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 296, "column": 12}, "end": {"line": 296, "column": 29}}, {"start": {"line": 296, "column": 33}, "end": {"line": 296, "column": 51}}, {"start": {"line": 296, "column": 55}, "end": {"line": 296, "column": 63}}], "line": 296}, "12": {"loc": {"start": {"line": 301, "column": 8}, "end": {"line": 304, "column": 9}}, "type": "if", "locations": [{"start": {"line": 301, "column": 8}, "end": {"line": 304, "column": 9}}, {"start": {}, "end": {}}], "line": 301}, "13": {"loc": {"start": {"line": 301, "column": 12}, "end": {"line": 301, "column": 93}}, "type": "binary-expr", "locations": [{"start": {"line": 301, "column": 12}, "end": {"line": 301, "column": 29}}, {"start": {"line": 301, "column": 33}, "end": {"line": 301, "column": 51}}, {"start": {"line": 301, "column": 55}, "end": {"line": 301, "column": 63}}, {"start": {"line": 301, "column": 67}, "end": {"line": 301, "column": 93}}], "line": 301}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0, 0], "12": [0, 0], "13": [0, 0, 0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\doctorController.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\doctorController.js", "statementMap": {"0": {"start": {"line": 2, "column": 11}, "end": {"line": 2, "column": 40}}, "1": {"start": {"line": 3, "column": 51}, "end": {"line": 3, "column": 71}}, "2": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 35}}, "3": {"start": {"line": 6, "column": 21}, "end": {"line": 37, "column": 1}}, "4": {"start": {"line": 7, "column": 4}, "end": {"line": 36, "column": 5}}, "5": {"start": {"line": 8, "column": 157}, "end": {"line": 8, "column": 165}}, "6": {"start": {"line": 11, "column": 21}, "end": {"line": 23, "column": 10}}, "7": {"start": {"line": 26, "column": 8}, "end": {"line": 30, "column": 11}}, "8": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": 81}}, "9": {"start": {"line": 34, "column": 8}, "end": {"line": 34, "column": 29}}, "10": {"start": {"line": 35, "column": 8}, "end": {"line": 35, "column": 78}}, "11": {"start": {"line": 39, "column": 18}, "end": {"line": 73, "column": 1}}, "12": {"start": {"line": 40, "column": 4}, "end": {"line": 72, "column": 5}}, "13": {"start": {"line": 41, "column": 23}, "end": {"line": 41, "column": 33}}, "14": {"start": {"line": 43, "column": 23}, "end": {"line": 62, "column": 10}}, "15": {"start": {"line": 64, "column": 8}, "end": {"line": 66, "column": 9}}, "16": {"start": {"line": 65, "column": 12}, "end": {"line": 65, "column": 78}}, "17": {"start": {"line": 68, "column": 8}, "end": {"line": 68, "column": 37}}, "18": {"start": {"line": 70, "column": 8}, "end": {"line": 70, "column": 29}}, "19": {"start": {"line": 71, "column": 8}, "end": {"line": 71, "column": 78}}, "20": {"start": {"line": 76, "column": 21}, "end": {"line": 122, "column": 1}}, "21": {"start": {"line": 77, "column": 4}, "end": {"line": 121, "column": 5}}, "22": {"start": {"line": 78, "column": 23}, "end": {"line": 78, "column": 33}}, "23": {"start": {"line": 79, "column": 157}, "end": {"line": 79, "column": 165}}, "24": {"start": {"line": 81, "column": 21}, "end": {"line": 81, "column": 44}}, "25": {"start": {"line": 83, "column": 8}, "end": {"line": 85, "column": 9}}, "26": {"start": {"line": 84, "column": 12}, "end": {"line": 84, "column": 78}}, "27": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 53}}, "28": {"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": 50}}, "29": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 41}}, "30": {"start": {"line": 91, "column": 8}, "end": {"line": 93, "column": 9}}, "31": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 47}}, "32": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": 44}}, "33": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 59}}, "34": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 56}}, "35": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 41}}, "36": {"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": 58}}, "37": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 26}}, "38": {"start": {"line": 104, "column": 29}, "end": {"line": 104, "column": 84}}, "39": {"start": {"line": 106, "column": 8}, "end": {"line": 116, "column": 9}}, "40": {"start": {"line": 107, "column": 12}, "end": {"line": 107, "column": 103}}, "41": {"start": {"line": 108, "column": 12}, "end": {"line": 108, "column": 91}}, "42": {"start": {"line": 109, "column": 12}, "end": {"line": 109, "column": 38}}, "43": {"start": {"line": 111, "column": 12}, "end": {"line": 115, "column": 15}}, "44": {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 92}}, "45": {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 29}}, "46": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 78}}, "47": {"start": {"line": 124, "column": 21}, "end": {"line": 142, "column": 1}}, "48": {"start": {"line": 125, "column": 4}, "end": {"line": 141, "column": 5}}, "49": {"start": {"line": 126, "column": 23}, "end": {"line": 126, "column": 33}}, "50": {"start": {"line": 128, "column": 21}, "end": {"line": 128, "column": 44}}, "51": {"start": {"line": 130, "column": 8}, "end": {"line": 132, "column": 9}}, "52": {"start": {"line": 131, "column": 12}, "end": {"line": 131, "column": 78}}, "53": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 64}}, "54": {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 29}}, "55": {"start": {"line": 137, "column": 8}, "end": {"line": 137, "column": 63}}, "56": {"start": {"line": 139, "column": 8}, "end": {"line": 139, "column": 29}}, "57": {"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": 78}}, "58": {"start": {"line": 147, "column": 22}, "end": {"line": 180, "column": 1}}, "59": {"start": {"line": 148, "column": 4}, "end": {"line": 179, "column": 5}}, "60": {"start": {"line": 149, "column": 24}, "end": {"line": 173, "column": 10}}, "61": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 26}}, "62": {"start": {"line": 177, "column": 8}, "end": {"line": 177, "column": 29}}, "63": {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 78}}, "64": {"start": {"line": 182, "column": 30}, "end": {"line": 227, "column": 1}}, "65": {"start": {"line": 183, "column": 4}, "end": {"line": 226, "column": 5}}, "66": {"start": {"line": 184, "column": 23}, "end": {"line": 184, "column": 33}}, "67": {"start": {"line": 187, "column": 8}, "end": {"line": 189, "column": 9}}, "68": {"start": {"line": 188, "column": 12}, "end": {"line": 188, "column": 77}}, "69": {"start": {"line": 191, "column": 8}, "end": {"line": 191, "column": 61}}, "70": {"start": {"line": 193, "column": 24}, "end": {"line": 219, "column": 10}}, "71": {"start": {"line": 221, "column": 8}, "end": {"line": 221, "column": 75}}, "72": {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 26}}, "73": {"start": {"line": 224, "column": 8}, "end": {"line": 224, "column": 64}}, "74": {"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 78}}, "75": {"start": {"line": 229, "column": 0}, "end": {"line": 236, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": 22}}, "loc": {"start": {"line": 6, "column": 41}, "end": {"line": 37, "column": 1}}, "line": 6}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 39, "column": 18}, "end": {"line": 39, "column": 19}}, "loc": {"start": {"line": 39, "column": 38}, "end": {"line": 73, "column": 1}}, "line": 39}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 76, "column": 21}, "end": {"line": 76, "column": 22}}, "loc": {"start": {"line": 76, "column": 41}, "end": {"line": 122, "column": 1}}, "line": 76}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 124, "column": 21}, "end": {"line": 124, "column": 22}}, "loc": {"start": {"line": 124, "column": 41}, "end": {"line": 142, "column": 1}}, "line": 124}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 147, "column": 22}, "end": {"line": 147, "column": 23}}, "loc": {"start": {"line": 147, "column": 42}, "end": {"line": 180, "column": 1}}, "line": 147}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 182, "column": 30}, "end": {"line": 182, "column": 31}}, "loc": {"start": {"line": 182, "column": 50}, "end": {"line": 227, "column": 1}}, "line": 182}}, "branchMap": {"0": {"loc": {"start": {"line": 64, "column": 8}, "end": {"line": 66, "column": 9}}, "type": "if", "locations": [{"start": {"line": 64, "column": 8}, "end": {"line": 66, "column": 9}}, {"start": {}, "end": {}}], "line": 64}, "1": {"loc": {"start": {"line": 83, "column": 8}, "end": {"line": 85, "column": 9}}, "type": "if", "locations": [{"start": {"line": 83, "column": 8}, "end": {"line": 85, "column": 9}}, {"start": {}, "end": {}}], "line": 83}, "2": {"loc": {"start": {"line": 88, "column": 25}, "end": {"line": 88, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 88, "column": 25}, "end": {"line": 88, "column": 34}}, {"start": {"line": 88, "column": 38}, "end": {"line": 88, "column": 52}}], "line": 88}, "3": {"loc": {"start": {"line": 89, "column": 24}, "end": {"line": 89, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 24}, "end": {"line": 89, "column": 32}}, {"start": {"line": 89, "column": 36}, "end": {"line": 89, "column": 49}}], "line": 89}, "4": {"loc": {"start": {"line": 90, "column": 21}, "end": {"line": 90, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 90, "column": 21}, "end": {"line": 90, "column": 26}}, {"start": {"line": 90, "column": 30}, "end": {"line": 90, "column": 40}}], "line": 90}, "5": {"loc": {"start": {"line": 91, "column": 8}, "end": {"line": 93, "column": 9}}, "type": "if", "locations": [{"start": {"line": 91, "column": 8}, "end": {"line": 93, "column": 9}}, {"start": {}, "end": {}}], "line": 91}, "6": {"loc": {"start": {"line": 94, "column": 23}, "end": {"line": 94, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 94, "column": 23}, "end": {"line": 94, "column": 30}}, {"start": {"line": 94, "column": 34}, "end": {"line": 94, "column": 46}}], "line": 94}, "7": {"loc": {"start": {"line": 95, "column": 22}, "end": {"line": 95, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 22}, "end": {"line": 95, "column": 28}}, {"start": {"line": 95, "column": 32}, "end": {"line": 95, "column": 43}}], "line": 95}, "8": {"loc": {"start": {"line": 96, "column": 27}, "end": {"line": 96, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 27}, "end": {"line": 96, "column": 38}}, {"start": {"line": 96, "column": 42}, "end": {"line": 96, "column": 58}}], "line": 96}, "9": {"loc": {"start": {"line": 97, "column": 26}, "end": {"line": 97, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 97, "column": 26}, "end": {"line": 97, "column": 36}}, {"start": {"line": 97, "column": 40}, "end": {"line": 97, "column": 55}}], "line": 97}, "10": {"loc": {"start": {"line": 98, "column": 21}, "end": {"line": 98, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 98, "column": 21}, "end": {"line": 98, "column": 26}}, {"start": {"line": 98, "column": 30}, "end": {"line": 98, "column": 40}}], "line": 98}, "11": {"loc": {"start": {"line": 99, "column": 27}, "end": {"line": 99, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 99, "column": 27}, "end": {"line": 99, "column": 38}}, {"start": {"line": 99, "column": 42}, "end": {"line": 99, "column": 58}}], "line": 99}, "12": {"loc": {"start": {"line": 106, "column": 8}, "end": {"line": 116, "column": 9}}, "type": "if", "locations": [{"start": {"line": 106, "column": 8}, "end": {"line": 116, "column": 9}}, {"start": {"line": 110, "column": 15}, "end": {"line": 116, "column": 9}}], "line": 106}, "13": {"loc": {"start": {"line": 107, "column": 47}, "end": {"line": 107, "column": 102}}, "type": "binary-expr", "locations": [{"start": {"line": 107, "column": 47}, "end": {"line": 107, "column": 66}}, {"start": {"line": 107, "column": 70}, "end": {"line": 107, "column": 102}}], "line": 107}, "14": {"loc": {"start": {"line": 108, "column": 43}, "end": {"line": 108, "column": 90}}, "type": "binary-expr", "locations": [{"start": {"line": 108, "column": 43}, "end": {"line": 108, "column": 58}}, {"start": {"line": 108, "column": 62}, "end": {"line": 108, "column": 90}}], "line": 108}, "15": {"loc": {"start": {"line": 130, "column": 8}, "end": {"line": 132, "column": 9}}, "type": "if", "locations": [{"start": {"line": 130, "column": 8}, "end": {"line": 132, "column": 9}}, {"start": {}, "end": {}}], "line": 130}, "16": {"loc": {"start": {"line": 187, "column": 8}, "end": {"line": 189, "column": 9}}, "type": "if", "locations": [{"start": {"line": 187, "column": 8}, "end": {"line": 189, "column": 9}}, {"start": {}, "end": {}}], "line": 187}, "17": {"loc": {"start": {"line": 187, "column": 12}, "end": {"line": 187, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 187, "column": 12}, "end": {"line": 187, "column": 15}}, {"start": {"line": 187, "column": 19}, "end": {"line": 187, "column": 38}}], "line": 187}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\scheduleController.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\scheduleController.js", "statementMap": {"0": {"start": {"line": 2, "column": 11}, "end": {"line": 2, "column": 40}}, "1": {"start": {"line": 3, "column": 47}, "end": {"line": 3, "column": 67}}, "2": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 35}}, "3": {"start": {"line": 8, "column": 27}, "end": {"line": 90, "column": 1}}, "4": {"start": {"line": 9, "column": 4}, "end": {"line": 89, "column": 5}}, "5": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 52}}, "6": {"start": {"line": 11, "column": 30}, "end": {"line": 11, "column": 44}}, "7": {"start": {"line": 13, "column": 8}, "end": {"line": 18, "column": 9}}, "8": {"start": {"line": 14, "column": 12}, "end": {"line": 17, "column": 15}}, "9": {"start": {"line": 22, "column": 8}, "end": {"line": 44, "column": 9}}, "10": {"start": {"line": 23, "column": 30}, "end": {"line": 23, "column": 51}}, "11": {"start": {"line": 24, "column": 12}, "end": {"line": 29, "column": 13}}, "12": {"start": {"line": 25, "column": 16}, "end": {"line": 28, "column": 19}}, "13": {"start": {"line": 30, "column": 12}, "end": {"line": 30, "column": 48}}, "14": {"start": {"line": 31, "column": 12}, "end": {"line": 36, "column": 13}}, "15": {"start": {"line": 32, "column": 16}, "end": {"line": 35, "column": 19}}, "16": {"start": {"line": 37, "column": 12}, "end": {"line": 37, "column": 46}}, "17": {"start": {"line": 39, "column": 26}, "end": {"line": 39, "column": 36}}, "18": {"start": {"line": 40, "column": 12}, "end": {"line": 40, "column": 39}}, "19": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 40}}, "20": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 38}}, "21": {"start": {"line": 43, "column": 12}, "end": {"line": 43, "column": 49}}, "22": {"start": {"line": 46, "column": 31}, "end": {"line": 46, "column": 68}}, "23": {"start": {"line": 47, "column": 29}, "end": {"line": 47, "column": 64}}, "24": {"start": {"line": 49, "column": 8}, "end": {"line": 49, "column": 111}}, "25": {"start": {"line": 51, "column": 26}, "end": {"line": 76, "column": 10}}, "26": {"start": {"line": 78, "column": 8}, "end": {"line": 81, "column": 11}}, "27": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 61}}, "28": {"start": {"line": 84, "column": 8}, "end": {"line": 88, "column": 11}}, "29": {"start": {"line": 94, "column": 24}, "end": {"line": 115, "column": 1}}, "30": {"start": {"line": 95, "column": 4}, "end": {"line": 114, "column": 5}}, "31": {"start": {"line": 96, "column": 26}, "end": {"line": 109, "column": 10}}, "32": {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 65}}, "33": {"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 29}}, "34": {"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 78}}, "35": {"start": {"line": 118, "column": 24}, "end": {"line": 134, "column": 1}}, "36": {"start": {"line": 119, "column": 4}, "end": {"line": 133, "column": 5}}, "37": {"start": {"line": 120, "column": 23}, "end": {"line": 120, "column": 33}}, "38": {"start": {"line": 121, "column": 25}, "end": {"line": 125, "column": 10}}, "39": {"start": {"line": 126, "column": 8}, "end": {"line": 128, "column": 9}}, "40": {"start": {"line": 127, "column": 12}, "end": {"line": 127, "column": 79}}, "41": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 39}}, "42": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 29}}, "43": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 78}}, "44": {"start": {"line": 137, "column": 23}, "end": {"line": 224, "column": 1}}, "45": {"start": {"line": 138, "column": 4}, "end": {"line": 223, "column": 5}}, "46": {"start": {"line": 139, "column": 56}, "end": {"line": 139, "column": 64}}, "47": {"start": {"line": 142, "column": 8}, "end": {"line": 147, "column": 9}}, "48": {"start": {"line": 143, "column": 12}, "end": {"line": 146, "column": 15}}, "49": {"start": {"line": 149, "column": 8}, "end": {"line": 154, "column": 9}}, "50": {"start": {"line": 150, "column": 12}, "end": {"line": 153, "column": 15}}, "51": {"start": {"line": 156, "column": 8}, "end": {"line": 161, "column": 9}}, "52": {"start": {"line": 157, "column": 12}, "end": {"line": 160, "column": 15}}, "53": {"start": {"line": 164, "column": 26}, "end": {"line": 164, "column": 47}}, "54": {"start": {"line": 165, "column": 8}, "end": {"line": 170, "column": 9}}, "55": {"start": {"line": 166, "column": 12}, "end": {"line": 169, "column": 15}}, "56": {"start": {"line": 173, "column": 8}, "end": {"line": 178, "column": 9}}, "57": {"start": {"line": 174, "column": 12}, "end": {"line": 177, "column": 15}}, "58": {"start": {"line": 181, "column": 33}, "end": {"line": 187, "column": 10}}, "59": {"start": {"line": 189, "column": 8}, "end": {"line": 194, "column": 9}}, "60": {"start": {"line": 190, "column": 12}, "end": {"line": 193, "column": 15}}, "61": {"start": {"line": 196, "column": 28}, "end": {"line": 202, "column": 10}}, "62": {"start": {"line": 205, "column": 37}, "end": {"line": 209, "column": 10}}, "63": {"start": {"line": 211, "column": 8}, "end": {"line": 215, "column": 11}}, "64": {"start": {"line": 217, "column": 8}, "end": {"line": 217, "column": 57}}, "65": {"start": {"line": 218, "column": 8}, "end": {"line": 222, "column": 11}}, "66": {"start": {"line": 227, "column": 23}, "end": {"line": 248, "column": 1}}, "67": {"start": {"line": 228, "column": 4}, "end": {"line": 247, "column": 5}}, "68": {"start": {"line": 229, "column": 23}, "end": {"line": 229, "column": 33}}, "69": {"start": {"line": 230, "column": 56}, "end": {"line": 230, "column": 64}}, "70": {"start": {"line": 232, "column": 25}, "end": {"line": 232, "column": 52}}, "71": {"start": {"line": 233, "column": 8}, "end": {"line": 235, "column": 9}}, "72": {"start": {"line": 234, "column": 12}, "end": {"line": 234, "column": 79}}, "73": {"start": {"line": 237, "column": 8}, "end": {"line": 237, "column": 82}}, "74": {"start": {"line": 238, "column": 8}, "end": {"line": 238, "column": 66}}, "75": {"start": {"line": 239, "column": 8}, "end": {"line": 239, "column": 82}}, "76": {"start": {"line": 240, "column": 8}, "end": {"line": 240, "column": 86}}, "77": {"start": {"line": 242, "column": 8}, "end": {"line": 242, "column": 30}}, "78": {"start": {"line": 243, "column": 8}, "end": {"line": 243, "column": 39}}, "79": {"start": {"line": 245, "column": 8}, "end": {"line": 245, "column": 29}}, "80": {"start": {"line": 246, "column": 8}, "end": {"line": 246, "column": 78}}, "81": {"start": {"line": 251, "column": 23}, "end": {"line": 264, "column": 1}}, "82": {"start": {"line": 252, "column": 4}, "end": {"line": 263, "column": 5}}, "83": {"start": {"line": 253, "column": 23}, "end": {"line": 253, "column": 33}}, "84": {"start": {"line": 254, "column": 25}, "end": {"line": 254, "column": 52}}, "85": {"start": {"line": 255, "column": 8}, "end": {"line": 257, "column": 9}}, "86": {"start": {"line": 256, "column": 12}, "end": {"line": 256, "column": 79}}, "87": {"start": {"line": 258, "column": 8}, "end": {"line": 258, "column": 33}}, "88": {"start": {"line": 259, "column": 8}, "end": {"line": 259, "column": 69}}, "89": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 29}}, "90": {"start": {"line": 262, "column": 8}, "end": {"line": 262, "column": 78}}, "91": {"start": {"line": 266, "column": 26}, "end": {"line": 288, "column": 1}}, "92": {"start": {"line": 267, "column": 4}, "end": {"line": 287, "column": 5}}, "93": {"start": {"line": 268, "column": 25}, "end": {"line": 268, "column": 44}}, "94": {"start": {"line": 269, "column": 22}, "end": {"line": 269, "column": 32}}, "95": {"start": {"line": 270, "column": 31}, "end": {"line": 270, "column": 41}}, "96": {"start": {"line": 271, "column": 8}, "end": {"line": 271, "column": 52}}, "97": {"start": {"line": 273, "column": 26}, "end": {"line": 281, "column": 10}}, "98": {"start": {"line": 283, "column": 8}, "end": {"line": 283, "column": 47}}, "99": {"start": {"line": 285, "column": 8}, "end": {"line": 285, "column": 51}}, "100": {"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 63}}, "101": {"start": {"line": 289, "column": 0}, "end": {"line": 296, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 28}}, "loc": {"start": {"line": 8, "column": 47}, "end": {"line": 90, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 94, "column": 24}, "end": {"line": 94, "column": 25}}, "loc": {"start": {"line": 94, "column": 44}, "end": {"line": 115, "column": 1}}, "line": 94}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 118, "column": 24}, "end": {"line": 118, "column": 25}}, "loc": {"start": {"line": 118, "column": 44}, "end": {"line": 134, "column": 1}}, "line": 118}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 137, "column": 23}, "end": {"line": 137, "column": 24}}, "loc": {"start": {"line": 137, "column": 43}, "end": {"line": 224, "column": 1}}, "line": 137}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 227, "column": 23}, "end": {"line": 227, "column": 24}}, "loc": {"start": {"line": 227, "column": 43}, "end": {"line": 248, "column": 1}}, "line": 227}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 251, "column": 23}, "end": {"line": 251, "column": 24}}, "loc": {"start": {"line": 251, "column": 43}, "end": {"line": 264, "column": 1}}, "line": 251}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 266, "column": 26}, "end": {"line": 266, "column": 27}}, "loc": {"start": {"line": 266, "column": 46}, "end": {"line": 288, "column": 1}}, "line": 266}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 8}, "end": {"line": 18, "column": 9}}, "type": "if", "locations": [{"start": {"line": 13, "column": 8}, "end": {"line": 18, "column": 9}}, {"start": {}, "end": {}}], "line": 13}, "1": {"loc": {"start": {"line": 22, "column": 8}, "end": {"line": 44, "column": 9}}, "type": "if", "locations": [{"start": {"line": 22, "column": 8}, "end": {"line": 44, "column": 9}}, {"start": {"line": 38, "column": 15}, "end": {"line": 44, "column": 9}}], "line": 22}, "2": {"loc": {"start": {"line": 24, "column": 12}, "end": {"line": 29, "column": 13}}, "type": "if", "locations": [{"start": {"line": 24, "column": 12}, "end": {"line": 29, "column": 13}}, {"start": {}, "end": {}}], "line": 24}, "3": {"loc": {"start": {"line": 31, "column": 12}, "end": {"line": 36, "column": 13}}, "type": "if", "locations": [{"start": {"line": 31, "column": 12}, "end": {"line": 36, "column": 13}}, {"start": {}, "end": {}}], "line": 31}, "4": {"loc": {"start": {"line": 126, "column": 8}, "end": {"line": 128, "column": 9}}, "type": "if", "locations": [{"start": {"line": 126, "column": 8}, "end": {"line": 128, "column": 9}}, {"start": {}, "end": {}}], "line": 126}, "5": {"loc": {"start": {"line": 142, "column": 8}, "end": {"line": 147, "column": 9}}, "type": "if", "locations": [{"start": {"line": 142, "column": 8}, "end": {"line": 147, "column": 9}}, {"start": {}, "end": {}}], "line": 142}, "6": {"loc": {"start": {"line": 149, "column": 8}, "end": {"line": 154, "column": 9}}, "type": "if", "locations": [{"start": {"line": 149, "column": 8}, "end": {"line": 154, "column": 9}}, {"start": {}, "end": {}}], "line": 149}, "7": {"loc": {"start": {"line": 156, "column": 8}, "end": {"line": 161, "column": 9}}, "type": "if", "locations": [{"start": {"line": 156, "column": 8}, "end": {"line": 161, "column": 9}}, {"start": {}, "end": {}}], "line": 156}, "8": {"loc": {"start": {"line": 156, "column": 12}, "end": {"line": 156, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 156, "column": 12}, "end": {"line": 156, "column": 21}}, {"start": {"line": 156, "column": 25}, "end": {"line": 156, "column": 47}}], "line": 156}, "9": {"loc": {"start": {"line": 165, "column": 8}, "end": {"line": 170, "column": 9}}, "type": "if", "locations": [{"start": {"line": 165, "column": 8}, "end": {"line": 170, "column": 9}}, {"start": {}, "end": {}}], "line": 165}, "10": {"loc": {"start": {"line": 173, "column": 8}, "end": {"line": 178, "column": 9}}, "type": "if", "locations": [{"start": {"line": 173, "column": 8}, "end": {"line": 178, "column": 9}}, {"start": {}, "end": {}}], "line": 173}, "11": {"loc": {"start": {"line": 173, "column": 12}, "end": {"line": 173, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 173, "column": 12}, "end": {"line": 173, "column": 22}}, {"start": {"line": 173, "column": 26}, "end": {"line": 173, "column": 39}}], "line": 173}, "12": {"loc": {"start": {"line": 189, "column": 8}, "end": {"line": 194, "column": 9}}, "type": "if", "locations": [{"start": {"line": 189, "column": 8}, "end": {"line": 194, "column": 9}}, {"start": {}, "end": {}}], "line": 189}, "13": {"loc": {"start": {"line": 200, "column": 23}, "end": {"line": 200, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 200, "column": 23}, "end": {"line": 200, "column": 32}}, {"start": {"line": 200, "column": 36}, "end": {"line": 200, "column": 37}}], "line": 200}, "14": {"loc": {"start": {"line": 233, "column": 8}, "end": {"line": 235, "column": 9}}, "type": "if", "locations": [{"start": {"line": 233, "column": 8}, "end": {"line": 235, "column": 9}}, {"start": {}, "end": {}}], "line": 233}, "15": {"loc": {"start": {"line": 237, "column": 28}, "end": {"line": 237, "column": 81}}, "type": "cond-expr", "locations": [{"start": {"line": 237, "column": 53}, "end": {"line": 237, "column": 61}}, {"start": {"line": 237, "column": 64}, "end": {"line": 237, "column": 81}}], "line": 237}, "16": {"loc": {"start": {"line": 238, "column": 24}, "end": {"line": 238, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 238, "column": 45}, "end": {"line": 238, "column": 49}}, {"start": {"line": 238, "column": 52}, "end": {"line": 238, "column": 65}}], "line": 238}, "17": {"loc": {"start": {"line": 239, "column": 28}, "end": {"line": 239, "column": 81}}, "type": "cond-expr", "locations": [{"start": {"line": 239, "column": 53}, "end": {"line": 239, "column": 61}}, {"start": {"line": 239, "column": 64}, "end": {"line": 239, "column": 81}}], "line": 239}, "18": {"loc": {"start": {"line": 240, "column": 29}, "end": {"line": 240, "column": 85}}, "type": "cond-expr", "locations": [{"start": {"line": 240, "column": 55}, "end": {"line": 240, "column": 64}}, {"start": {"line": 240, "column": 67}, "end": {"line": 240, "column": 85}}], "line": 240}, "19": {"loc": {"start": {"line": 255, "column": 8}, "end": {"line": 257, "column": 9}}, "type": "if", "locations": [{"start": {"line": 255, "column": 8}, "end": {"line": 257, "column": 9}}, {"start": {}, "end": {}}], "line": 255}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\specialtyController.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\specialtyController.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 10, "column": 2}}, "2": {"start": {"line": 4, "column": 4}, "end": {"line": 9, "column": 5}}, "3": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 53}}, "4": {"start": {"line": 6, "column": 8}, "end": {"line": 6, "column": 42}}, "5": {"start": {"line": 8, "column": 8}, "end": {"line": 8, "column": 94}}, "6": {"start": {"line": 12, "column": 0}, "end": {"line": 33, "column": 2}}, "7": {"start": {"line": 13, "column": 4}, "end": {"line": 32, "column": 5}}, "8": {"start": {"line": 14, "column": 23}, "end": {"line": 14, "column": 33}}, "9": {"start": {"line": 17, "column": 8}, "end": {"line": 19, "column": 9}}, "10": {"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 77}}, "11": {"start": {"line": 22, "column": 26}, "end": {"line": 22, "column": 54}}, "12": {"start": {"line": 24, "column": 8}, "end": {"line": 26, "column": 9}}, "13": {"start": {"line": 25, "column": 12}, "end": {"line": 25, "column": 90}}, "14": {"start": {"line": 28, "column": 8}, "end": {"line": 28, "column": 40}}, "15": {"start": {"line": 30, "column": 8}, "end": {"line": 30, "column": 58}}, "16": {"start": {"line": 31, "column": 8}, "end": {"line": 31, "column": 100}}, "17": {"start": {"line": 36, "column": 0}, "end": {"line": 55, "column": 2}}, "18": {"start": {"line": 37, "column": 4}, "end": {"line": 54, "column": 5}}, "19": {"start": {"line": 38, "column": 45}, "end": {"line": 38, "column": 53}}, "20": {"start": {"line": 40, "column": 8}, "end": {"line": 42, "column": 9}}, "21": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 90}}, "22": {"start": {"line": 44, "column": 29}, "end": {"line": 48, "column": 10}}, "23": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 101}}, "24": {"start": {"line": 52, "column": 8}, "end": {"line": 52, "column": 58}}, "25": {"start": {"line": 53, "column": 8}, "end": {"line": 53, "column": 92}}, "26": {"start": {"line": 58, "column": 0}, "end": {"line": 77, "column": 2}}, "27": {"start": {"line": 59, "column": 4}, "end": {"line": 76, "column": 5}}, "28": {"start": {"line": 60, "column": 23}, "end": {"line": 60, "column": 33}}, "29": {"start": {"line": 61, "column": 38}, "end": {"line": 61, "column": 46}}, "30": {"start": {"line": 63, "column": 26}, "end": {"line": 63, "column": 54}}, "31": {"start": {"line": 64, "column": 8}, "end": {"line": 66, "column": 9}}, "32": {"start": {"line": 65, "column": 12}, "end": {"line": 65, "column": 90}}, "33": {"start": {"line": 68, "column": 8}, "end": {"line": 68, "column": 48}}, "34": {"start": {"line": 69, "column": 8}, "end": {"line": 69, "column": 69}}, "35": {"start": {"line": 71, "column": 8}, "end": {"line": 71, "column": 31}}, "36": {"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 87}}, "37": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": 58}}, "38": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 92}}, "39": {"start": {"line": 80, "column": 0}, "end": {"line": 96, "column": 2}}, "40": {"start": {"line": 81, "column": 4}, "end": {"line": 95, "column": 5}}, "41": {"start": {"line": 82, "column": 23}, "end": {"line": 82, "column": 33}}, "42": {"start": {"line": 84, "column": 26}, "end": {"line": 84, "column": 54}}, "43": {"start": {"line": 86, "column": 8}, "end": {"line": 88, "column": 9}}, "44": {"start": {"line": 87, "column": 12}, "end": {"line": 87, "column": 76}}, "45": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 34}}, "46": {"start": {"line": 92, "column": 8}, "end": {"line": 92, "column": 76}}, "47": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 92}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 29}}, "loc": {"start": {"line": 3, "column": 48}, "end": {"line": 10, "column": 1}}, "line": 3}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 28}}, "loc": {"start": {"line": 12, "column": 47}, "end": {"line": 33, "column": 1}}, "line": 12}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 36, "column": 26}, "end": {"line": 36, "column": 27}}, "loc": {"start": {"line": 36, "column": 46}, "end": {"line": 55, "column": 1}}, "line": 36}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 58, "column": 26}, "end": {"line": 58, "column": 27}}, "loc": {"start": {"line": 58, "column": 46}, "end": {"line": 77, "column": 1}}, "line": 58}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 80, "column": 26}, "end": {"line": 80, "column": 27}}, "loc": {"start": {"line": 80, "column": 46}, "end": {"line": 96, "column": 1}}, "line": 80}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 8}, "end": {"line": 19, "column": 9}}, "type": "if", "locations": [{"start": {"line": 17, "column": 8}, "end": {"line": 19, "column": 9}}, {"start": {}, "end": {}}], "line": 17}, "1": {"loc": {"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 15}}, {"start": {"line": 17, "column": 19}, "end": {"line": 17, "column": 38}}], "line": 17}, "2": {"loc": {"start": {"line": 24, "column": 8}, "end": {"line": 26, "column": 9}}, "type": "if", "locations": [{"start": {"line": 24, "column": 8}, "end": {"line": 26, "column": 9}}, {"start": {}, "end": {}}], "line": 24}, "3": {"loc": {"start": {"line": 40, "column": 8}, "end": {"line": 42, "column": 9}}, "type": "if", "locations": [{"start": {"line": 40, "column": 8}, "end": {"line": 42, "column": 9}}, {"start": {}, "end": {}}], "line": 40}, "4": {"loc": {"start": {"line": 40, "column": 12}, "end": {"line": 40, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 12}, "end": {"line": 40, "column": 17}}, {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 33}}], "line": 40}, "5": {"loc": {"start": {"line": 64, "column": 8}, "end": {"line": 66, "column": 9}}, "type": "if", "locations": [{"start": {"line": 64, "column": 8}, "end": {"line": 66, "column": 9}}, {"start": {}, "end": {}}], "line": 64}, "6": {"loc": {"start": {"line": 68, "column": 25}, "end": {"line": 68, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 68, "column": 25}, "end": {"line": 68, "column": 29}}, {"start": {"line": 68, "column": 33}, "end": {"line": 68, "column": 47}}], "line": 68}, "7": {"loc": {"start": {"line": 69, "column": 32}, "end": {"line": 69, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 69, "column": 32}, "end": {"line": 69, "column": 43}}, {"start": {"line": 69, "column": 47}, "end": {"line": 69, "column": 68}}], "line": 69}, "8": {"loc": {"start": {"line": 86, "column": 8}, "end": {"line": 88, "column": 9}}, "type": "if", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 88, "column": 9}}, {"start": {}, "end": {}}], "line": 86}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\userController.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\controllers\\userController.js", "statementMap": {"0": {"start": {"line": 2, "column": 13}, "end": {"line": 2, "column": 38}}, "1": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 34}}, "2": {"start": {"line": 4, "column": 12}, "end": {"line": 4, "column": 35}}, "3": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 47}}, "4": {"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": 54}}, "5": {"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 28}}, "6": {"start": {"line": 8, "column": 11}, "end": {"line": 8, "column": 24}}, "7": {"start": {"line": 13, "column": 21}, "end": {"line": 50, "column": 1}}, "8": {"start": {"line": 14, "column": 4}, "end": {"line": 49, "column": 5}}, "9": {"start": {"line": 15, "column": 87}, "end": {"line": 15, "column": 95}}, "10": {"start": {"line": 18, "column": 27}, "end": {"line": 18, "column": 67}}, "11": {"start": {"line": 19, "column": 8}, "end": {"line": 21, "column": 9}}, "12": {"start": {"line": 20, "column": 12}, "end": {"line": 20, "column": 78}}, "13": {"start": {"line": 24, "column": 21}, "end": {"line": 32, "column": 10}}, "14": {"start": {"line": 37, "column": 8}, "end": {"line": 45, "column": 11}}, "15": {"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 29}}, "16": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 78}}, "17": {"start": {"line": 52, "column": 16}, "end": {"line": 69, "column": 1}}, "18": {"start": {"line": 53, "column": 4}, "end": {"line": 68, "column": 5}}, "19": {"start": {"line": 54, "column": 23}, "end": {"line": 54, "column": 36}}, "20": {"start": {"line": 56, "column": 21}, "end": {"line": 58, "column": 10}}, "21": {"start": {"line": 60, "column": 8}, "end": {"line": 62, "column": 9}}, "22": {"start": {"line": 61, "column": 12}, "end": {"line": 61, "column": 82}}, "23": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 23}}, "24": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 29}}, "25": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 78}}, "26": {"start": {"line": 71, "column": 19}, "end": {"line": 105, "column": 1}}, "27": {"start": {"line": 72, "column": 4}, "end": {"line": 104, "column": 5}}, "28": {"start": {"line": 73, "column": 23}, "end": {"line": 73, "column": 36}}, "29": {"start": {"line": 74, "column": 114}, "end": {"line": 74, "column": 122}}, "30": {"start": {"line": 76, "column": 21}, "end": {"line": 76, "column": 48}}, "31": {"start": {"line": 78, "column": 8}, "end": {"line": 80, "column": 9}}, "32": {"start": {"line": 79, "column": 12}, "end": {"line": 79, "column": 82}}, "33": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 53}}, "34": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 50}}, "35": {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 41}}, "36": {"start": {"line": 86, "column": 8}, "end": {"line": 89, "column": 9}}, "37": {"start": {"line": 87, "column": 25}, "end": {"line": 87, "column": 49}}, "38": {"start": {"line": 88, "column": 12}, "end": {"line": 88, "column": 62}}, "39": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 59}}, "40": {"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 47}}, "41": {"start": {"line": 92, "column": 8}, "end": {"line": 92, "column": 44}}, "42": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 56}}, "43": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 44}}, "44": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": 41}}, "45": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 68}}, "46": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 26}}, "47": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 88}}, "48": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 29}}, "49": {"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 78}}, "50": {"start": {"line": 110, "column": 19}, "end": {"line": 127, "column": 1}}, "51": {"start": {"line": 111, "column": 4}, "end": {"line": 126, "column": 5}}, "52": {"start": {"line": 112, "column": 23}, "end": {"line": 112, "column": 36}}, "53": {"start": {"line": 114, "column": 21}, "end": {"line": 114, "column": 48}}, "54": {"start": {"line": 116, "column": 8}, "end": {"line": 118, "column": 9}}, "55": {"start": {"line": 117, "column": 12}, "end": {"line": 117, "column": 82}}, "56": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 29}}, "57": {"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 67}}, "58": {"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 29}}, "59": {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 78}}, "60": {"start": {"line": 130, "column": 24}, "end": {"line": 164, "column": 1}}, "61": {"start": {"line": 131, "column": 4}, "end": {"line": 163, "column": 5}}, "62": {"start": {"line": 132, "column": 87}, "end": {"line": 132, "column": 95}}, "63": {"start": {"line": 135, "column": 27}, "end": {"line": 135, "column": 67}}, "64": {"start": {"line": 136, "column": 8}, "end": {"line": 138, "column": 9}}, "65": {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 78}}, "66": {"start": {"line": 141, "column": 21}, "end": {"line": 150, "column": 10}}, "67": {"start": {"line": 152, "column": 8}, "end": {"line": 159, "column": 11}}, "68": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 29}}, "69": {"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 78}}, "70": {"start": {"line": 167, "column": 23}, "end": {"line": 236, "column": 1}}, "71": {"start": {"line": 168, "column": 4}, "end": {"line": 235, "column": 5}}, "72": {"start": {"line": 170, "column": 8}, "end": {"line": 172, "column": 9}}, "73": {"start": {"line": 171, "column": 12}, "end": {"line": 171, "column": 90}}, "74": {"start": {"line": 187, "column": 12}, "end": {"line": 187, "column": 20}}, "75": {"start": {"line": 190, "column": 27}, "end": {"line": 190, "column": 67}}, "76": {"start": {"line": 191, "column": 8}, "end": {"line": 193, "column": 9}}, "77": {"start": {"line": 192, "column": 12}, "end": {"line": 192, "column": 78}}, "78": {"start": {"line": 196, "column": 23}, "end": {"line": 222, "column": 10}}, "79": {"start": {"line": 198, "column": 25}, "end": {"line": 210, "column": 34}}, "80": {"start": {"line": 213, "column": 12}, "end": {"line": 219, "column": 13}}, "81": {"start": {"line": 214, "column": 16}, "end": {"line": 218, "column": 39}}, "82": {"start": {"line": 221, "column": 12}, "end": {"line": 221, "column": 24}}, "83": {"start": {"line": 224, "column": 8}, "end": {"line": 231, "column": 11}}, "84": {"start": {"line": 233, "column": 8}, "end": {"line": 233, "column": 29}}, "85": {"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 78}}, "86": {"start": {"line": 239, "column": 22}, "end": {"line": 289, "column": 1}}, "87": {"start": {"line": 240, "column": 4}, "end": {"line": 288, "column": 5}}, "88": {"start": {"line": 242, "column": 8}, "end": {"line": 244, "column": 9}}, "89": {"start": {"line": 243, "column": 12}, "end": {"line": 243, "column": 94}}, "90": {"start": {"line": 255, "column": 12}, "end": {"line": 255, "column": 20}}, "91": {"start": {"line": 258, "column": 27}, "end": {"line": 258, "column": 67}}, "92": {"start": {"line": 259, "column": 8}, "end": {"line": 261, "column": 9}}, "93": {"start": {"line": 260, "column": 12}, "end": {"line": 260, "column": 78}}, "94": {"start": {"line": 264, "column": 21}, "end": {"line": 275, "column": 10}}, "95": {"start": {"line": 277, "column": 8}, "end": {"line": 284, "column": 11}}, "96": {"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 29}}, "97": {"start": {"line": 287, "column": 8}, "end": {"line": 287, "column": 78}}, "98": {"start": {"line": 294, "column": 26}, "end": {"line": 349, "column": 1}}, "99": {"start": {"line": 295, "column": 4}, "end": {"line": 348, "column": 5}}, "100": {"start": {"line": 297, "column": 8}, "end": {"line": 299, "column": 9}}, "101": {"start": {"line": 298, "column": 12}, "end": {"line": 298, "column": 104}}, "102": {"start": {"line": 301, "column": 22}, "end": {"line": 320, "column": 10}}, "103": {"start": {"line": 323, "column": 29}, "end": {"line": 327, "column": 9}}, "104": {"start": {"line": 329, "column": 8}, "end": {"line": 334, "column": 11}}, "105": {"start": {"line": 330, "column": 29}, "end": {"line": 330, "column": 54}}, "106": {"start": {"line": 331, "column": 12}, "end": {"line": 333, "column": 13}}, "107": {"start": {"line": 332, "column": 16}, "end": {"line": 332, "column": 57}}, "108": {"start": {"line": 336, "column": 8}, "end": {"line": 340, "column": 11}}, "109": {"start": {"line": 342, "column": 8}, "end": {"line": 342, "column": 29}}, "110": {"start": {"line": 343, "column": 8}, "end": {"line": 347, "column": 11}}, "111": {"start": {"line": 352, "column": 27}, "end": {"line": 375, "column": 1}}, "112": {"start": {"line": 353, "column": 4}, "end": {"line": 374, "column": 5}}, "113": {"start": {"line": 354, "column": 8}, "end": {"line": 354, "column": 54}}, "114": {"start": {"line": 355, "column": 8}, "end": {"line": 355, "column": 43}}, "115": {"start": {"line": 356, "column": 8}, "end": {"line": 356, "column": 43}}, "116": {"start": {"line": 357, "column": 8}, "end": {"line": 360, "column": 9}}, "117": {"start": {"line": 358, "column": 12}, "end": {"line": 358, "column": 42}}, "118": {"start": {"line": 359, "column": 12}, "end": {"line": 359, "column": 71}}, "119": {"start": {"line": 361, "column": 8}, "end": {"line": 364, "column": 9}}, "120": {"start": {"line": 362, "column": 12}, "end": {"line": 362, "column": 45}}, "121": {"start": {"line": 363, "column": 12}, "end": {"line": 363, "column": 74}}, "122": {"start": {"line": 366, "column": 25}, "end": {"line": 366, "column": 64}}, "123": {"start": {"line": 367, "column": 8}, "end": {"line": 367, "column": 34}}, "124": {"start": {"line": 368, "column": 8}, "end": {"line": 368, "column": 30}}, "125": {"start": {"line": 369, "column": 8}, "end": {"line": 369, "column": 53}}, "126": {"start": {"line": 370, "column": 8}, "end": {"line": 370, "column": 31}}, "127": {"start": {"line": 372, "column": 8}, "end": {"line": 372, "column": 48}}, "128": {"start": {"line": 373, "column": 8}, "end": {"line": 373, "column": 78}}, "129": {"start": {"line": 377, "column": 0}, "end": {"line": 387, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 22}}, "loc": {"start": {"line": 13, "column": 41}, "end": {"line": 50, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 17}}, "loc": {"start": {"line": 52, "column": 36}, "end": {"line": 69, "column": 1}}, "line": 52}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 71, "column": 19}, "end": {"line": 71, "column": 20}}, "loc": {"start": {"line": 71, "column": 39}, "end": {"line": 105, "column": 1}}, "line": 71}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 110, "column": 19}, "end": {"line": 110, "column": 20}}, "loc": {"start": {"line": 110, "column": 39}, "end": {"line": 127, "column": 1}}, "line": 110}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 130, "column": 24}, "end": {"line": 130, "column": 25}}, "loc": {"start": {"line": 130, "column": 44}, "end": {"line": 164, "column": 1}}, "line": 130}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 167, "column": 23}, "end": {"line": 167, "column": 24}}, "loc": {"start": {"line": 167, "column": 43}, "end": {"line": 236, "column": 1}}, "line": 167}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 196, "column": 51}, "end": {"line": 196, "column": 52}}, "loc": {"start": {"line": 196, "column": 64}, "end": {"line": 222, "column": 9}}, "line": 196}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 239, "column": 22}, "end": {"line": 239, "column": 23}}, "loc": {"start": {"line": 239, "column": 42}, "end": {"line": 289, "column": 1}}, "line": 239}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 294, "column": 26}, "end": {"line": 294, "column": 27}}, "loc": {"start": {"line": 294, "column": 46}, "end": {"line": 349, "column": 1}}, "line": 294}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 329, "column": 22}, "end": {"line": 329, "column": 23}}, "loc": {"start": {"line": 329, "column": 30}, "end": {"line": 334, "column": 9}}, "line": 329}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 352, "column": 27}, "end": {"line": 352, "column": 28}}, "loc": {"start": {"line": 352, "column": 47}, "end": {"line": 375, "column": 1}}, "line": 352}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 8}, "end": {"line": 21, "column": 9}}, "type": "if", "locations": [{"start": {"line": 19, "column": 8}, "end": {"line": 21, "column": 9}}, {"start": {}, "end": {}}], "line": 19}, "1": {"loc": {"start": {"line": 60, "column": 8}, "end": {"line": 62, "column": 9}}, "type": "if", "locations": [{"start": {"line": 60, "column": 8}, "end": {"line": 62, "column": 9}}, {"start": {}, "end": {}}], "line": 60}, "2": {"loc": {"start": {"line": 78, "column": 8}, "end": {"line": 80, "column": 9}}, "type": "if", "locations": [{"start": {"line": 78, "column": 8}, "end": {"line": 80, "column": 9}}, {"start": {}, "end": {}}], "line": 78}, "3": {"loc": {"start": {"line": 83, "column": 25}, "end": {"line": 83, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 25}, "end": {"line": 83, "column": 34}}, {"start": {"line": 83, "column": 38}, "end": {"line": 83, "column": 52}}], "line": 83}, "4": {"loc": {"start": {"line": 84, "column": 24}, "end": {"line": 84, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 24}, "end": {"line": 84, "column": 32}}, {"start": {"line": 84, "column": 36}, "end": {"line": 84, "column": 49}}], "line": 84}, "5": {"loc": {"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": 26}}, {"start": {"line": 85, "column": 30}, "end": {"line": 85, "column": 40}}], "line": 85}, "6": {"loc": {"start": {"line": 86, "column": 8}, "end": {"line": 89, "column": 9}}, "type": "if", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 89, "column": 9}}, {"start": {}, "end": {}}], "line": 86}, "7": {"loc": {"start": {"line": 90, "column": 27}, "end": {"line": 90, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 90, "column": 27}, "end": {"line": 90, "column": 38}}, {"start": {"line": 90, "column": 42}, "end": {"line": 90, "column": 58}}], "line": 90}, "8": {"loc": {"start": {"line": 91, "column": 23}, "end": {"line": 91, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 23}, "end": {"line": 91, "column": 30}}, {"start": {"line": 91, "column": 34}, "end": {"line": 91, "column": 46}}], "line": 91}, "9": {"loc": {"start": {"line": 92, "column": 22}, "end": {"line": 92, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 92, "column": 22}, "end": {"line": 92, "column": 28}}, {"start": {"line": 92, "column": 32}, "end": {"line": 92, "column": 43}}], "line": 92}, "10": {"loc": {"start": {"line": 93, "column": 26}, "end": {"line": 93, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 93, "column": 26}, "end": {"line": 93, "column": 36}}, {"start": {"line": 93, "column": 40}, "end": {"line": 93, "column": 55}}], "line": 93}, "11": {"loc": {"start": {"line": 94, "column": 22}, "end": {"line": 94, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 94, "column": 22}, "end": {"line": 94, "column": 28}}, {"start": {"line": 94, "column": 32}, "end": {"line": 94, "column": 43}}], "line": 94}, "12": {"loc": {"start": {"line": 95, "column": 21}, "end": {"line": 95, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 21}, "end": {"line": 95, "column": 26}}, {"start": {"line": 95, "column": 30}, "end": {"line": 95, "column": 40}}], "line": 95}, "13": {"loc": {"start": {"line": 96, "column": 27}, "end": {"line": 96, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 27}, "end": {"line": 96, "column": 47}}, {"start": {"line": 96, "column": 51}, "end": {"line": 96, "column": 67}}], "line": 96}, "14": {"loc": {"start": {"line": 116, "column": 8}, "end": {"line": 118, "column": 9}}, "type": "if", "locations": [{"start": {"line": 116, "column": 8}, "end": {"line": 118, "column": 9}}, {"start": {}, "end": {}}], "line": 116}, "15": {"loc": {"start": {"line": 136, "column": 8}, "end": {"line": 138, "column": 9}}, "type": "if", "locations": [{"start": {"line": 136, "column": 8}, "end": {"line": 138, "column": 9}}, {"start": {}, "end": {}}], "line": 136}, "16": {"loc": {"start": {"line": 170, "column": 8}, "end": {"line": 172, "column": 9}}, "type": "if", "locations": [{"start": {"line": 170, "column": 8}, "end": {"line": 172, "column": 9}}, {"start": {}, "end": {}}], "line": 170}, "17": {"loc": {"start": {"line": 170, "column": 12}, "end": {"line": 170, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 170, "column": 12}, "end": {"line": 170, "column": 21}}, {"start": {"line": 170, "column": 25}, "end": {"line": 170, "column": 49}}], "line": 170}, "18": {"loc": {"start": {"line": 191, "column": 8}, "end": {"line": 193, "column": 9}}, "type": "if", "locations": [{"start": {"line": 191, "column": 8}, "end": {"line": 193, "column": 9}}, {"start": {}, "end": {}}], "line": 191}, "19": {"loc": {"start": {"line": 207, "column": 28}, "end": {"line": 207, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 207, "column": 28}, "end": {"line": 207, "column": 38}}, {"start": {"line": 207, "column": 42}, "end": {"line": 207, "column": 46}}], "line": 207}, "20": {"loc": {"start": {"line": 213, "column": 12}, "end": {"line": 219, "column": 13}}, "type": "if", "locations": [{"start": {"line": 213, "column": 12}, "end": {"line": 219, "column": 13}}, {"start": {}, "end": {}}], "line": 213}, "21": {"loc": {"start": {"line": 213, "column": 16}, "end": {"line": 213, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 213, "column": 16}, "end": {"line": 213, "column": 35}}, {"start": {"line": 213, "column": 39}, "end": {"line": 213, "column": 54}}], "line": 213}, "22": {"loc": {"start": {"line": 216, "column": 41}, "end": {"line": 216, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 216, "column": 41}, "end": {"line": 216, "column": 60}}, {"start": {"line": 216, "column": 64}, "end": {"line": 216, "column": 66}}], "line": 216}, "23": {"loc": {"start": {"line": 217, "column": 37}, "end": {"line": 217, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 217, "column": 37}, "end": {"line": 217, "column": 52}}, {"start": {"line": 217, "column": 56}, "end": {"line": 217, "column": 58}}], "line": 217}, "24": {"loc": {"start": {"line": 242, "column": 8}, "end": {"line": 244, "column": 9}}, "type": "if", "locations": [{"start": {"line": 242, "column": 8}, "end": {"line": 244, "column": 9}}, {"start": {}, "end": {}}], "line": 242}, "25": {"loc": {"start": {"line": 242, "column": 12}, "end": {"line": 242, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 242, "column": 12}, "end": {"line": 242, "column": 21}}, {"start": {"line": 242, "column": 25}, "end": {"line": 242, "column": 49}}], "line": 242}, "26": {"loc": {"start": {"line": 259, "column": 8}, "end": {"line": 261, "column": 9}}, "type": "if", "locations": [{"start": {"line": 259, "column": 8}, "end": {"line": 261, "column": 9}}, {"start": {}, "end": {}}], "line": 259}, "27": {"loc": {"start": {"line": 297, "column": 8}, "end": {"line": 299, "column": 9}}, "type": "if", "locations": [{"start": {"line": 297, "column": 8}, "end": {"line": 299, "column": 9}}, {"start": {}, "end": {}}], "line": 297}, "28": {"loc": {"start": {"line": 297, "column": 12}, "end": {"line": 297, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 297, "column": 12}, "end": {"line": 297, "column": 21}}, {"start": {"line": 297, "column": 25}, "end": {"line": 297, "column": 49}}], "line": 297}, "29": {"loc": {"start": {"line": 331, "column": 12}, "end": {"line": 333, "column": 13}}, "type": "if", "locations": [{"start": {"line": 331, "column": 12}, "end": {"line": 333, "column": 13}}, {"start": {}, "end": {}}], "line": 331}, "30": {"loc": {"start": {"line": 357, "column": 8}, "end": {"line": 360, "column": 9}}, "type": "if", "locations": [{"start": {"line": 357, "column": 8}, "end": {"line": 360, "column": 9}}, {"start": {}, "end": {}}], "line": 357}, "31": {"loc": {"start": {"line": 361, "column": 8}, "end": {"line": 364, "column": 9}}, "type": "if", "locations": [{"start": {"line": 361, "column": 8}, "end": {"line": 364, "column": 9}}, {"start": {}, "end": {}}], "line": 361}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\lib\\auth.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\lib\\auth.js", "statementMap": {"0": {"start": {"line": 2, "column": 12}, "end": {"line": 2, "column": 35}}, "1": {"start": {"line": 5, "column": 22}, "end": {"line": 7, "column": 1}}, "2": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 72}}, "3": {"start": {"line": 10, "column": 20}, "end": {"line": 17, "column": 1}}, "4": {"start": {"line": 11, "column": 2}, "end": {"line": 16, "column": 3}}, "5": {"start": {"line": 12, "column": 20}, "end": {"line": 12, "column": 61}}, "6": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 19}}, "7": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 16}}, "8": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 48}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 23}}, "loc": {"start": {"line": 5, "column": 35}, "end": {"line": 7, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 21}}, "loc": {"start": {"line": 10, "column": 31}, "end": {"line": 17, "column": 1}}, "line": 10}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\middleware\\authMiddleware.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\middleware\\authMiddleware.js", "statementMap": {"0": {"start": {"line": 2, "column": 12}, "end": {"line": 2, "column": 35}}, "1": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 38}}, "2": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 46}}, "3": {"start": {"line": 6, "column": 16}, "end": {"line": 41, "column": 1}}, "4": {"start": {"line": 9, "column": 2}, "end": {"line": 36, "column": 3}}, "5": {"start": {"line": 10, "column": 4}, "end": {"line": 35, "column": 5}}, "6": {"start": {"line": 12, "column": 6}, "end": {"line": 12, "column": 54}}, "7": {"start": {"line": 15, "column": 22}, "end": {"line": 15, "column": 40}}, "8": {"start": {"line": 17, "column": 6}, "end": {"line": 19, "column": 7}}, "9": {"start": {"line": 18, "column": 8}, "end": {"line": 18, "column": 88}}, "10": {"start": {"line": 22, "column": 19}, "end": {"line": 24, "column": 8}}, "11": {"start": {"line": 26, "column": 6}, "end": {"line": 28, "column": 7}}, "12": {"start": {"line": 27, "column": 8}, "end": {"line": 27, "column": 94}}, "13": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 22}}, "14": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 13}}, "15": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 27}}, "16": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 79}}, "17": {"start": {"line": 38, "column": 2}, "end": {"line": 40, "column": 3}}, "18": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 73}}, "19": {"start": {"line": 44, "column": 23}, "end": {"line": 51, "column": 1}}, "20": {"start": {"line": 45, "column": 2}, "end": {"line": 50, "column": 4}}, "21": {"start": {"line": 46, "column": 4}, "end": {"line": 48, "column": 5}}, "22": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 92}}, "23": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 11}}, "24": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 45}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 16}, "end": {"line": 6, "column": 17}}, "loc": {"start": {"line": 6, "column": 42}, "end": {"line": 41, "column": 1}}, "line": 6}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 44, "column": 23}, "end": {"line": 44, "column": 24}}, "loc": {"start": {"line": 44, "column": 37}, "end": {"line": 51, "column": 1}}, "line": 44}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 10}}, "loc": {"start": {"line": 45, "column": 29}, "end": {"line": 50, "column": 3}}, "line": 45}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 2}, "end": {"line": 36, "column": 3}}, "type": "if", "locations": [{"start": {"line": 9, "column": 2}, "end": {"line": 36, "column": 3}}, {"start": {}, "end": {}}], "line": 9}, "1": {"loc": {"start": {"line": 9, "column": 6}, "end": {"line": 9, "column": 81}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 6}, "end": {"line": 9, "column": 31}}, {"start": {"line": 9, "column": 35}, "end": {"line": 9, "column": 81}}], "line": 9}, "2": {"loc": {"start": {"line": 17, "column": 6}, "end": {"line": 19, "column": 7}}, "type": "if", "locations": [{"start": {"line": 17, "column": 6}, "end": {"line": 19, "column": 7}}, {"start": {}, "end": {}}], "line": 17}, "3": {"loc": {"start": {"line": 26, "column": 6}, "end": {"line": 28, "column": 7}}, "type": "if", "locations": [{"start": {"line": 26, "column": 6}, "end": {"line": 28, "column": 7}}, {"start": {}, "end": {}}], "line": 26}, "4": {"loc": {"start": {"line": 38, "column": 2}, "end": {"line": 40, "column": 3}}, "type": "if", "locations": [{"start": {"line": 38, "column": 2}, "end": {"line": 40, "column": 3}}, {"start": {}, "end": {}}], "line": 38}, "5": {"loc": {"start": {"line": 46, "column": 4}, "end": {"line": 48, "column": 5}}, "type": "if", "locations": [{"start": {"line": 46, "column": 4}, "end": {"line": 48, "column": 5}}, {"start": {}, "end": {}}], "line": 46}, "6": {"loc": {"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": 17}}, {"start": {"line": 46, "column": 21}, "end": {"line": 46, "column": 53}}], "line": 46}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\middleware\\verifyToken.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\middleware\\verifyToken.js", "statementMap": {"0": {"start": {"line": 1, "column": 12}, "end": {"line": 1, "column": 35}}, "1": {"start": {"line": 3, "column": 20}, "end": {"line": 19, "column": 1}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 48}}, "3": {"start": {"line": 6, "column": 4}, "end": {"line": 8, "column": 5}}, "4": {"start": {"line": 7, "column": 8}, "end": {"line": 7, "column": 71}}, "5": {"start": {"line": 10, "column": 18}, "end": {"line": 10, "column": 42}}, "6": {"start": {"line": 12, "column": 4}, "end": {"line": 18, "column": 5}}, "7": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 65}}, "8": {"start": {"line": 14, "column": 8}, "end": {"line": 14, "column": 27}}, "9": {"start": {"line": 15, "column": 8}, "end": {"line": 15, "column": 15}}, "10": {"start": {"line": 17, "column": 8}, "end": {"line": 17, "column": 87}}, "11": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 29}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 20}, "end": {"line": 3, "column": 21}}, "loc": {"start": {"line": 3, "column": 40}, "end": {"line": 19, "column": 1}}, "line": 3}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 4}, "end": {"line": 8, "column": 5}}, "type": "if", "locations": [{"start": {"line": 6, "column": 4}, "end": {"line": 8, "column": 5}}, {"start": {}, "end": {}}], "line": 6}, "1": {"loc": {"start": {"line": 6, "column": 8}, "end": {"line": 6, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 8}, "end": {"line": 6, "column": 19}}, {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 56}}], "line": 6}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\1migration_Allcode.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\1migration_Allcode.js", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 36, "column": 2}}, "1": {"start": {"line": 3, "column": 4}, "end": {"line": 31, "column": 7}}, "2": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 47}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 2, "column": 6}, "end": {"line": 2, "column": 7}}, "loc": {"start": {"line": 2, "column": 43}, "end": {"line": 32, "column": 3}}, "line": 2}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 33, "column": 8}, "end": {"line": 33, "column": 9}}, "loc": {"start": {"line": 33, "column": 45}, "end": {"line": 35, "column": 3}}, "line": 33}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\2migration_Specialty.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\2migration_Specialty.js", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 32, "column": 2}}, "1": {"start": {"line": 3, "column": 4}, "end": {"line": 27, "column": 7}}, "2": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 50}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 2, "column": 6}, "end": {"line": 2, "column": 7}}, "loc": {"start": {"line": 2, "column": 43}, "end": {"line": 28, "column": 3}}, "line": 2}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 9}}, "loc": {"start": {"line": 29, "column": 45}, "end": {"line": 31, "column": 3}}, "line": 29}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\3migration_User.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\3migration_User.js", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 75, "column": 2}}, "1": {"start": {"line": 3, "column": 4}, "end": {"line": 70, "column": 7}}, "2": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 44}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 2, "column": 6}, "end": {"line": 2, "column": 7}}, "loc": {"start": {"line": 2, "column": 43}, "end": {"line": 71, "column": 3}}, "line": 2}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 9}}, "loc": {"start": {"line": 72, "column": 45}, "end": {"line": 74, "column": 3}}, "line": 72}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\4migration_Booking.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\4migration_Booking.js", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 65, "column": 2}}, "1": {"start": {"line": 3, "column": 4}, "end": {"line": 60, "column": 7}}, "2": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 47}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 2, "column": 6}, "end": {"line": 2, "column": 7}}, "loc": {"start": {"line": 2, "column": 43}, "end": {"line": 61, "column": 3}}, "line": 2}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 9}}, "loc": {"start": {"line": 62, "column": 45}, "end": {"line": 64, "column": 3}}, "line": 62}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\5migration_Schedule.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\5migration_Schedule.js", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 44, "column": 2}}, "1": {"start": {"line": 3, "column": 4}, "end": {"line": 39, "column": 7}}, "2": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 48}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 2, "column": 6}, "end": {"line": 2, "column": 7}}, "loc": {"start": {"line": 2, "column": 43}, "end": {"line": 40, "column": 3}}, "line": 2}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 9}}, "loc": {"start": {"line": 41, "column": 45}, "end": {"line": 43, "column": 3}}, "line": 41}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\6migration_DoctorDetail.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\migrations\\6migration_DoctorDetail.js", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 40, "column": 2}}, "1": {"start": {"line": 3, "column": 4}, "end": {"line": 35, "column": 7}}, "2": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 52}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 2, "column": 6}, "end": {"line": 2, "column": 7}}, "loc": {"start": {"line": 2, "column": 43}, "end": {"line": 36, "column": 3}}, "line": 2}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": 9}}, "loc": {"start": {"line": 37, "column": 45}, "end": {"line": 39, "column": 3}}, "line": 37}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\Allcode.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\Allcode.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 47}}, "2": {"start": {"line": 4, "column": 16}, "end": {"line": 12, "column": 2}}, "3": {"start": {"line": 14, "column": 0}, "end": {"line": 20, "column": 2}}, "4": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 94}}, "5": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 102}}, "6": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 84}}, "7": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 83}}, "8": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 103}}, "9": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 25}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 21}}, "loc": {"start": {"line": 14, "column": 38}, "end": {"line": 20, "column": 1}}, "line": 14}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\Booking.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\Booking.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 47}}, "2": {"start": {"line": 4, "column": 16}, "end": {"line": 14, "column": 2}}, "3": {"start": {"line": 16, "column": 0}, "end": {"line": 21, "column": 2}}, "4": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 103}}, "5": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 105}}, "6": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 79}}, "7": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 81}}, "8": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 25}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 16, "column": 20}, "end": {"line": 16, "column": 21}}, "loc": {"start": {"line": 16, "column": 38}, "end": {"line": 21, "column": 1}}, "line": 16}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\DoctorDetail.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\DoctorDetail.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 47}}, "2": {"start": {"line": 4, "column": 21}, "end": {"line": 20, "column": 2}}, "3": {"start": {"line": 22, "column": 0}, "end": {"line": 27, "column": 2}}, "4": {"start": {"line": 23, "column": 2}, "end": {"line": 26, "column": 5}}, "5": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 30}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 22, "column": 25}, "end": {"line": 22, "column": 26}}, "loc": {"start": {"line": 22, "column": 43}, "end": {"line": 27, "column": 1}}, "line": 22}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\Schedule.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\Schedule.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 47}}, "2": {"start": {"line": 4, "column": 17}, "end": {"line": 54, "column": 2}}, "3": {"start": {"line": 56, "column": 0}, "end": {"line": 59, "column": 2}}, "4": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 80}}, "5": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 106}}, "6": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 26}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 56, "column": 21}, "end": {"line": 56, "column": 22}}, "loc": {"start": {"line": 56, "column": 39}, "end": {"line": 59, "column": 1}}, "line": 56}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\Specialty.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\Specialty.js", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 47}}, "2": {"start": {"line": 4, "column": 18}, "end": {"line": 12, "column": 2}}, "3": {"start": {"line": 14, "column": 0}, "end": {"line": 17, "column": 2}}, "4": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 85}}, "5": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 22}, "end": {"line": 14, "column": 23}}, "loc": {"start": {"line": 14, "column": 40}, "end": {"line": 17, "column": 1}}, "line": 14}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\User.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\User.js", "statementMap": {"0": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 42}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": 47}}, "2": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 34}}, "3": {"start": {"line": 6, "column": 13}, "end": {"line": 51, "column": 2}}, "4": {"start": {"line": 34, "column": 6}, "end": {"line": 37, "column": 7}}, "5": {"start": {"line": 35, "column": 21}, "end": {"line": 35, "column": 45}}, "6": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 63}}, "7": {"start": {"line": 40, "column": 6}, "end": {"line": 43, "column": 7}}, "8": {"start": {"line": 41, "column": 21}, "end": {"line": 41, "column": 45}}, "9": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 63}}, "10": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 57}}, "11": {"start": {"line": 53, "column": 0}, "end": {"line": 60, "column": 2}}, "12": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": 96}}, "13": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 104}}, "14": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 78}}, "15": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 60}}, "16": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 83}}, "17": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 87}}, "18": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 33, "column": 18}, "end": {"line": 33, "column": 19}}, "loc": {"start": {"line": 33, "column": 34}, "end": {"line": 38, "column": 5}}, "line": 33}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 39, "column": 18}, "end": {"line": 39, "column": 19}}, "loc": {"start": {"line": 39, "column": 34}, "end": {"line": 44, "column": 5}}, "line": 39}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 47, "column": 19}, "end": {"line": 47, "column": 20}}, "loc": {"start": {"line": 47, "column": 39}, "end": {"line": 49, "column": 5}}, "line": 47}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 53, "column": 17}, "end": {"line": 53, "column": 18}}, "loc": {"start": {"line": 53, "column": 35}, "end": {"line": 60, "column": 1}}, "line": 53}}, "branchMap": {"0": {"loc": {"start": {"line": 34, "column": 6}, "end": {"line": 37, "column": 7}}, "type": "if", "locations": [{"start": {"line": 34, "column": 6}, "end": {"line": 37, "column": 7}}, {"start": {}, "end": {}}], "line": 34}, "1": {"loc": {"start": {"line": 40, "column": 6}, "end": {"line": 43, "column": 7}}, "type": "if", "locations": [{"start": {"line": 40, "column": 6}, "end": {"line": 43, "column": 7}}, {"start": {}, "end": {}}], "line": 40}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\index.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\models\\index.js", "statementMap": {"0": {"start": {"line": 2, "column": 11}, "end": {"line": 2, "column": 24}}, "1": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 28}}, "2": {"start": {"line": 4, "column": 18}, "end": {"line": 4, "column": 47}}, "3": {"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 42}}, "4": {"start": {"line": 6, "column": 11}, "end": {"line": 6, "column": 13}}, "5": {"start": {"line": 9, "column": 0}, "end": {"line": 16, "column": 7}}, "6": {"start": {"line": 11, "column": 8}, "end": {"line": 11, "column": 94}}, "7": {"start": {"line": 14, "column": 22}, "end": {"line": 14, "column": 57}}, "8": {"start": {"line": 15, "column": 8}, "end": {"line": 15, "column": 31}}, "9": {"start": {"line": 19, "column": 0}, "end": {"line": 23, "column": 3}}, "10": {"start": {"line": 20, "column": 4}, "end": {"line": 22, "column": 5}}, "11": {"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": 36}}, "12": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 25}}, "13": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 35}}, "14": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 20}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 12}, "end": {"line": 10, "column": 13}}, "loc": {"start": {"line": 10, "column": 20}, "end": {"line": 12, "column": 5}}, "line": 10}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 14}}, "loc": {"start": {"line": 13, "column": 21}, "end": {"line": 16, "column": 5}}, "line": 13}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 19, "column": 24}, "end": {"line": 19, "column": 25}}, "loc": {"start": {"line": 19, "column": 37}, "end": {"line": 23, "column": 1}}, "line": 19}}, "branchMap": {"0": {"loc": {"start": {"line": 11, "column": 15}, "end": {"line": 11, "column": 93}}, "type": "binary-expr", "locations": [{"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 39}}, {"start": {"line": 11, "column": 45}, "end": {"line": 11, "column": 62}}, {"start": {"line": 11, "column": 68}, "end": {"line": 11, "column": 92}}], "line": 11}, "1": {"loc": {"start": {"line": 20, "column": 4}, "end": {"line": 22, "column": 5}}, "type": "if", "locations": [{"start": {"line": 20, "column": 4}, "end": {"line": 22, "column": 5}}, {"start": {}, "end": {}}], "line": 20}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0, 0], "1": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\allcodeRoute.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\allcodeRoute.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 31}}, "2": {"start": {"line": 3, "column": 36}, "end": {"line": 3, "column": 75}}, "3": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 47}}, "4": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 70}}, "5": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 65}}, "6": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 68}}, "7": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 60}}, "8": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 66}}, "9": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 69}}, "10": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\authRoute.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\authRoute.js", "statementMap": {"0": {"start": {"line": 2, "column": 16}, "end": {"line": 2, "column": 34}}, "1": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 31}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 63}}, "3": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 56}}, "4": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 48}}, "5": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 62}}, "6": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\bookingRoute.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\bookingRoute.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 31}}, "2": {"start": {"line": 3, "column": 26}, "end": {"line": 3, "column": 69}}, "3": {"start": {"line": 4, "column": 36}, "end": {"line": 4, "column": 75}}, "4": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 81}}, "5": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 108}}, "6": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 111}}, "7": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 102}}, "8": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 96}}, "9": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 108}}, "10": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\doctorRoute.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\doctorRoute.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 31}}, "2": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 67}}, "3": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 55}}, "4": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 48}}, "5": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 47}}, "6": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 50}}, "7": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 53}}, "8": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 48}}, "9": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 69}}, "10": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\scheduleRoute.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\scheduleRoute.js", "statementMap": {"0": {"start": {"line": 2, "column": 16}, "end": {"line": 2, "column": 34}}, "1": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 31}}, "2": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 71}}, "3": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 55}}, "4": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 52}}, "5": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 55}}, "6": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 52}}, "7": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 54}}, "8": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 57}}, "9": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 71}}, "10": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\specialtyRoute.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\specialtyRoute.js", "statementMap": {"0": {"start": {"line": 2, "column": 16}, "end": {"line": 2, "column": 34}}, "1": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 31}}, "2": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 73}}, "3": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 55}}, "4": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 57}}, "5": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 54}}, "6": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 56}}, "7": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 59}}, "8": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 24}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\userRoute.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\routes\\userRoute.js", "statementMap": {"0": {"start": {"line": 2, "column": 16}, "end": {"line": 2, "column": 34}}, "1": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 31}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 63}}, "3": {"start": {"line": 5, "column": 36}, "end": {"line": 5, "column": 75}}, "4": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 32}}, "5": {"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 28}}, "6": {"start": {"line": 10, "column": 16}, "end": {"line": 19, "column": 2}}, "7": {"start": {"line": 12, "column": 8}, "end": {"line": 12, "column": 64}}, "8": {"start": {"line": 15, "column": 20}, "end": {"line": 15, "column": 51}}, "9": {"start": {"line": 16, "column": 25}, "end": {"line": 16, "column": 81}}, "10": {"start": {"line": 17, "column": 8}, "end": {"line": 17, "column": 27}}, "11": {"start": {"line": 20, "column": 15}, "end": {"line": 20, "column": 34}}, "12": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 84}}, "13": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 54}}, "14": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 65}}, "15": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 94}}, "16": {"start": {"line": 237, "column": 0}, "end": {"line": 237, "column": 92}}, "17": {"start": {"line": 262, "column": 0}, "end": {"line": 262, "column": 52}}, "18": {"start": {"line": 302, "column": 0}, "end": {"line": 302, "column": 55}}, "19": {"start": {"line": 327, "column": 0}, "end": {"line": 327, "column": 58}}, "20": {"start": {"line": 330, "column": 0}, "end": {"line": 330, "column": 98}}, "21": {"start": {"line": 332, "column": 0}, "end": {"line": 332, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": 18}}, "loc": {"start": {"line": 11, "column": 42}, "end": {"line": 13, "column": 5}}, "line": 11}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 15}}, "loc": {"start": {"line": 14, "column": 39}, "end": {"line": 18, "column": 5}}, "line": 14}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\1allcodeSeeder.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\1allcodeSeeder.js", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 33, "column": 2}}, "1": {"start": {"line": 4, "column": 8}, "end": {"line": 27, "column": 15}}, "2": {"start": {"line": 31, "column": 8}, "end": {"line": 31, "column": 63}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 8}, "end": {"line": 3, "column": 9}}, "loc": {"start": {"line": 3, "column": 45}, "end": {"line": 28, "column": 5}}, "line": 3}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 30, "column": 10}, "end": {"line": 30, "column": 11}}, "loc": {"start": {"line": 30, "column": 47}, "end": {"line": 32, "column": 5}}, "line": 30}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\3userSeeder.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\3userSeeder.js", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 33}}, "1": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 34}}, "2": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": 44}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 112, "column": 2}}, "4": {"start": {"line": 7, "column": 31}, "end": {"line": 7, "column": 62}}, "5": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 24}}, "6": {"start": {"line": 10, "column": 8}, "end": {"line": 25, "column": 9}}, "7": {"start": {"line": 10, "column": 21}, "end": {"line": 10, "column": 22}}, "8": {"start": {"line": 11, "column": 12}, "end": {"line": 24, "column": 15}}, "9": {"start": {"line": 44, "column": 12}, "end": {"line": 57, "column": 15}}, "10": {"start": {"line": 59, "column": 12}, "end": {"line": 72, "column": 15}}, "11": {"start": {"line": 74, "column": 12}, "end": {"line": 87, "column": 15}}, "12": {"start": {"line": 91, "column": 8}, "end": {"line": 104, "column": 11}}, "13": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 61}}, "14": {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 60}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 8}, "end": {"line": 6, "column": 9}}, "loc": {"start": {"line": 6, "column": 45}, "end": {"line": 107, "column": 5}}, "line": 6}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 109, "column": 10}, "end": {"line": 109, "column": 11}}, "loc": {"start": {"line": 109, "column": 47}, "end": {"line": 111, "column": 5}}, "line": 109}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\4bookingSeeder.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\4bookingSeeder.js", "statementMap": {"0": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 38}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": 44}}, "2": {"start": {"line": 5, "column": 0}, "end": {"line": 68, "column": 2}}, "3": {"start": {"line": 8, "column": 25}, "end": {"line": 11, "column": 9}}, "4": {"start": {"line": 13, "column": 8}, "end": {"line": 16, "column": 9}}, "5": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 67}}, "6": {"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 19}}, "7": {"start": {"line": 19, "column": 24}, "end": {"line": 22, "column": 9}}, "8": {"start": {"line": 24, "column": 8}, "end": {"line": 27, "column": 9}}, "9": {"start": {"line": 25, "column": 12}, "end": {"line": 25, "column": 64}}, "10": {"start": {"line": 26, "column": 12}, "end": {"line": 26, "column": 19}}, "11": {"start": {"line": 30, "column": 26}, "end": {"line": 33, "column": 9}}, "12": {"start": {"line": 35, "column": 8}, "end": {"line": 38, "column": 9}}, "13": {"start": {"line": 36, "column": 12}, "end": {"line": 36, "column": 66}}, "14": {"start": {"line": 37, "column": 12}, "end": {"line": 37, "column": 19}}, "15": {"start": {"line": 40, "column": 25}, "end": {"line": 40, "column": 27}}, "16": {"start": {"line": 41, "column": 28}, "end": {"line": 41, "column": 29}}, "17": {"start": {"line": 43, "column": 8}, "end": {"line": 60, "column": 9}}, "18": {"start": {"line": 43, "column": 21}, "end": {"line": 43, "column": 22}}, "19": {"start": {"line": 44, "column": 34}, "end": {"line": 44, "column": 70}}, "20": {"start": {"line": 45, "column": 33}, "end": {"line": 45, "column": 68}}, "21": {"start": {"line": 46, "column": 35}, "end": {"line": 46, "column": 72}}, "22": {"start": {"line": 48, "column": 32}, "end": {"line": 48, "column": 51}}, "23": {"start": {"line": 50, "column": 12}, "end": {"line": 59, "column": 15}}, "24": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 67}}, "25": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 63}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 8}, "end": {"line": 6, "column": 9}}, "loc": {"start": {"line": 6, "column": 45}, "end": {"line": 63, "column": 5}}, "line": 6}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 65, "column": 10}, "end": {"line": 65, "column": 11}}, "loc": {"start": {"line": 65, "column": 47}, "end": {"line": 67, "column": 5}}, "line": 65}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 8}, "end": {"line": 16, "column": 9}}, "type": "if", "locations": [{"start": {"line": 13, "column": 8}, "end": {"line": 16, "column": 9}}, {"start": {}, "end": {}}], "line": 13}, "1": {"loc": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 21}}, {"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 46}}], "line": 13}, "2": {"loc": {"start": {"line": 24, "column": 8}, "end": {"line": 27, "column": 9}}, "type": "if", "locations": [{"start": {"line": 24, "column": 8}, "end": {"line": 27, "column": 9}}, {"start": {}, "end": {}}], "line": 24}, "3": {"loc": {"start": {"line": 24, "column": 12}, "end": {"line": 24, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 12}, "end": {"line": 24, "column": 20}}, {"start": {"line": 24, "column": 24}, "end": {"line": 24, "column": 44}}], "line": 24}, "4": {"loc": {"start": {"line": 35, "column": 8}, "end": {"line": 38, "column": 9}}, "type": "if", "locations": [{"start": {"line": 35, "column": 8}, "end": {"line": 38, "column": 9}}, {"start": {}, "end": {}}], "line": 35}, "5": {"loc": {"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": 22}}, {"start": {"line": 35, "column": 26}, "end": {"line": 35, "column": 48}}], "line": 35}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\5schedulesSeeder.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\5schedulesSeeder.js", "statementMap": {"0": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 44}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 54, "column": 2}}, "2": {"start": {"line": 7, "column": 24}, "end": {"line": 10, "column": 9}}, "3": {"start": {"line": 12, "column": 8}, "end": {"line": 15, "column": 9}}, "4": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 65}}, "5": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 19}}, "6": {"start": {"line": 18, "column": 26}, "end": {"line": 21, "column": 9}}, "7": {"start": {"line": 23, "column": 8}, "end": {"line": 26, "column": 9}}, "8": {"start": {"line": 24, "column": 12}, "end": {"line": 24, "column": 67}}, "9": {"start": {"line": 25, "column": 12}, "end": {"line": 25, "column": 19}}, "10": {"start": {"line": 28, "column": 26}, "end": {"line": 28, "column": 28}}, "11": {"start": {"line": 29, "column": 24}, "end": {"line": 29, "column": 25}}, "12": {"start": {"line": 31, "column": 8}, "end": {"line": 46, "column": 9}}, "13": {"start": {"line": 31, "column": 21}, "end": {"line": 31, "column": 22}}, "14": {"start": {"line": 32, "column": 32}, "end": {"line": 32, "column": 42}}, "15": {"start": {"line": 33, "column": 12}, "end": {"line": 33, "column": 59}}, "16": {"start": {"line": 35, "column": 12}, "end": {"line": 45, "column": 13}}, "17": {"start": {"line": 36, "column": 16}, "end": {"line": 44, "column": 17}}, "18": {"start": {"line": 37, "column": 20}, "end": {"line": 43, "column": 23}}, "19": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 69}}, "20": {"start": {"line": 52, "column": 8}, "end": {"line": 52, "column": 64}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 8}, "end": {"line": 5, "column": 9}}, "loc": {"start": {"line": 5, "column": 45}, "end": {"line": 49, "column": 5}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 51, "column": 10}, "end": {"line": 51, "column": 11}}, "loc": {"start": {"line": 51, "column": 47}, "end": {"line": 53, "column": 5}}, "line": 51}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 8}, "end": {"line": 15, "column": 9}}, "type": "if", "locations": [{"start": {"line": 12, "column": 8}, "end": {"line": 15, "column": 9}}, {"start": {}, "end": {}}], "line": 12}, "1": {"loc": {"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 20}}, {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 44}}], "line": 12}, "2": {"loc": {"start": {"line": 23, "column": 8}, "end": {"line": 26, "column": 9}}, "type": "if", "locations": [{"start": {"line": 23, "column": 8}, "end": {"line": 26, "column": 9}}, {"start": {}, "end": {}}], "line": 23}, "3": {"loc": {"start": {"line": 23, "column": 12}, "end": {"line": 23, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 23, "column": 12}, "end": {"line": 23, "column": 22}}, {"start": {"line": 23, "column": 26}, "end": {"line": 23, "column": 48}}], "line": 23}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\6doctordetailSeeder.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\6doctordetailSeeder.js", "statementMap": {"0": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 44}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 31, "column": 2}}, "2": {"start": {"line": 7, "column": 24}, "end": {"line": 10, "column": 9}}, "3": {"start": {"line": 12, "column": 8}, "end": {"line": 15, "column": 9}}, "4": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 69}}, "5": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 19}}, "6": {"start": {"line": 17, "column": 30}, "end": {"line": 23, "column": 11}}, "7": {"start": {"line": 17, "column": 53}, "end": {"line": 23, "column": 9}}, "8": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 77}}, "9": {"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 68}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 8}, "end": {"line": 5, "column": 9}}, "loc": {"start": {"line": 5, "column": 45}, "end": {"line": 26, "column": 5}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 42}, "end": {"line": 17, "column": 43}}, "loc": {"start": {"line": 17, "column": 53}, "end": {"line": 23, "column": 9}}, "line": 17}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 10}, "end": {"line": 28, "column": 11}}, "loc": {"start": {"line": 28, "column": 47}, "end": {"line": 30, "column": 5}}, "line": 28}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 8}, "end": {"line": 15, "column": 9}}, "type": "if", "locations": [{"start": {"line": 12, "column": 8}, "end": {"line": 15, "column": 9}}, {"start": {}, "end": {}}], "line": 12}, "1": {"loc": {"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 20}}, {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 44}}], "line": 12}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\specialtySeeder.js": {"path": "F:\\CNPM\\CNPM_WebSiteDKKhamBenh\\backend\\src\\seeders\\specialtySeeder.js", "statementMap": {"0": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 44}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 24, "column": 2}}, "2": {"start": {"line": 6, "column": 28}, "end": {"line": 6, "column": 30}}, "3": {"start": {"line": 7, "column": 8}, "end": {"line": 16, "column": 9}}, "4": {"start": {"line": 7, "column": 21}, "end": {"line": 7, "column": 22}}, "5": {"start": {"line": 8, "column": 12}, "end": {"line": 15, "column": 15}}, "6": {"start": {"line": 18, "column": 8}, "end": {"line": 18, "column": 73}}, "7": {"start": {"line": 22, "column": 8}, "end": {"line": 22, "column": 66}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 8}, "end": {"line": 5, "column": 9}}, "loc": {"start": {"line": 5, "column": 45}, "end": {"line": 19, "column": 5}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 11}}, "loc": {"start": {"line": 21, "column": 47}, "end": {"line": 23, "column": 5}}, "line": 21}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0, "1": 0}, "b": {}}}