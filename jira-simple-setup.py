import requests
import time
from datetime import datetime

# <PERSON><PERSON><PERSON> hình <PERSON>ra
JIRA_URL = "https://tongnguyen.atlassian.net"
EMAIL = "<EMAIL>"
API_TOKEN = "ATATT3xFfGF0yLX54vcIAXoRm_eOdN2cT4DLvwVkVNlOYyTQrNhak5fxOD5g5hw7kel4pqWRAE055hLg3wZopfsiVSKBNCyqbJHGV06PqT0Q66FDsLwYmOHRyg2LW0UCr73MShS2ome-MvbHV3b3311ITK7XSZpLc2g0eHbe75e-KdU5Ddxkofg=FAA7516C"

PROJECT_KEY = "DEMO"

auth = (EMAIL, API_TOKEN)
headers = {"Content-Type": "application/json"}

# User mapping
USER_MAPPING = {
    "Tong Nguyen": "712020:9a00af4f-80fc-4906-9797-fb256ef2f737",
    "An Nguyễn": "712020:b5b2c1e0-c5b2-4853-9348-7469fa3e05c8",
    "Minh Trí": "712020:ef33d5b6-f721-4c84-bf11-388e2ed8ad7c",
}

def get_board_id():
    """Lấy board ID của project"""
    url = f"{JIRA_URL}/rest/agile/1.0/board"
    params = {"projectKeyOrId": PROJECT_KEY}
    response = requests.get(url, params=params, auth=auth, headers=headers)
    
    if response.status_code == 200:
        boards = response.json()["values"]
        if boards:
            return boards[0]["id"]
    return None

def create_epic(summary, description, assignee):
    """Tạo Epic"""
    url = f"{JIRA_URL}/rest/api/3/issue"
    
    assignee_id = USER_MAPPING.get(assignee, USER_MAPPING["Tong Nguyen"])
    
    payload = {
        "fields": {
            "project": {"key": PROJECT_KEY},
            "summary": summary,
            "description": {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [{"type": "text", "text": description}],
                    }
                ],
            },
            "issuetype": {"name": "Epic"},
            "assignee": {"accountId": assignee_id},
        }
    }
    
    response = requests.post(url, json=payload, auth=auth, headers=headers)
    
    if response.status_code == 201:
        issue_key = response.json()["key"]
        print(f"✅ Epic: {summary}... ({issue_key})")
        return issue_key
    else:
        print(f"❌ Failed to create epic: {summary}")
        return None

def create_story(summary, description, assignee, story_points, epic_key=None, acceptance_criteria=""):
    """Tạo Story"""
    url = f"{JIRA_URL}/rest/api/3/issue"
    
    assignee_id = USER_MAPPING.get(assignee, USER_MAPPING["Tong Nguyen"])
    
    # Tạo description với acceptance criteria
    full_description = f"{description}\n\n**Acceptance Criteria:**\n{acceptance_criteria}"
    
    payload = {
        "fields": {
            "project": {"key": PROJECT_KEY},
            "summary": summary,
            "description": {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [{"type": "text", "text": full_description}],
                    }
                ],
            },
            "issuetype": {"name": "Story"},
            "assignee": {"accountId": assignee_id},
            "customfield_10016": story_points,  # Story Points field
        }
    }
    
    # Link to epic if provided
    if epic_key:
        payload["fields"]["parent"] = {"key": epic_key}
    
    response = requests.post(url, json=payload, auth=auth, headers=headers)
    
    if response.status_code == 201:
        issue_key = response.json()["key"]
        print(f"✅ Story: {summary}... ({issue_key}) - {story_points} pts")
        if epic_key:
            print(f"   ⚠️ Could not link {issue_key} to {epic_key} - continuing without epic link")
        return issue_key
    else:
        print(f"❌ Failed to create story: {summary}")
        return None

def create_sprint(name, board_id):
    """Tạo Sprint"""
    url = f"{JIRA_URL}/rest/agile/1.0/sprint"
    
    payload = {
        "name": name,
        "originBoardId": board_id,
    }
    
    response = requests.post(url, json=payload, auth=auth, headers=headers)
    
    if response.status_code == 201:
        sprint_id = response.json()["id"]
        print(f"✅ Created sprint: {name} (ID: {sprint_id})")
        return sprint_id
    else:
        print(f"❌ Failed to create sprint: {name}")
        return None

def assign_stories_to_sprint(sprint_id, story_keys):
    """Assign stories vào sprint"""
    url = f"{JIRA_URL}/rest/agile/1.0/sprint/{sprint_id}/issue"
    
    payload = {"issues": story_keys}
    
    response = requests.post(url, json=payload, auth=auth, headers=headers)
    
    if response.status_code == 204:
        print(f"✅ Assigned {len(story_keys)} stories to sprint {sprint_id}")
        return True
    else:
        print(f"❌ Failed to assign stories to sprint {sprint_id}")
        return False

def setup_simple_project():
    """Setup đơn giản: chỉ tạo issues và sprints"""
    print(f"🚀 Setting up simple project {PROJECT_KEY}...")
    
    # 1. Tạo Epics
    print(f"\n📋 Creating Epics...")
    epics = {}
    
    epic_data = [
        ("Epic: Quản lý bệnh nhân", "Quản lý thông tin và hồ sơ bệnh nhân trong hệ thống", "Tong Nguyen"),
        ("Epic: Quản lý bác sĩ", "Quản lý thông tin bác sĩ và lịch làm việc", "An Nguyễn"),
        ("Epic: Đặt lịch khám", "Hệ thống đặt lịch khám bệnh trực tuyến", "Minh Trí"),
        ("Epic: Trang quản trị", "Trang quản trị hệ thống cho admin", "Tong Nguyen"),
    ]
    
    for summary, description, assignee in epic_data:
        epic_key = create_epic(summary, description, assignee)
        if epic_key:
            epics[summary] = epic_key
        time.sleep(0.5)
    
    # 2. Tạo Stories
    print(f"\n📝 Creating Stories...")
    stories = []
    
    story_data = [
        # Sprint 1: Doctors (2 stories)
        ("Quản lý hồ sơ bác sĩ", "Là một bác sĩ, tôi muốn quản lý hồ sơ cá nhân và lịch làm việc, nhờ đó tôi có thể hiển thị thông tin chính xác cho bệnh nhân", "Tong Nguyen", 8, "Epic: Quản lý bác sĩ", "- Cập nhật thông tin cá nhân\n- Thiết lập lịch làm việc\n- Quản lý chuyên khoa"),
        ("Xem danh sách bác sĩ theo chuyên khoa", "Là một người dùng, tôi muốn xem danh sách bác sĩ theo chuyên khoa, nhờ đó tôi có thể chọn bác sĩ phù hợp", "An Nguyễn", 5, "Epic: Quản lý bác sĩ", "- Hiển thị danh sách theo chuyên khoa\n- Thông tin cơ bản bác sĩ\n- Lọc và tìm kiếm"),
        
        # Sprint 2: Auth (5 stories)
        ("Đăng nhập hệ thống", "Là một người dùng, tôi muốn đăng nhập/đăng xuất, nhờ đó tôi có thể bảo vệ thông tin cá nhân của mình", "Minh Trí", 6, "Epic: Quản lý bệnh nhân", "- Form đăng nhập an toàn\n- Xác thực thông tin\n- Quản lý phiên đăng nhập"),
        ("Đăng ký tài khoản", "Là một người dùng mới, tôi muốn đăng ký tài khoản, nhờ đó tôi có thể sử dụng hệ thống để đặt lịch khám", "Tong Nguyen", 5, "Epic: Quản lý bệnh nhân", "- Form đăng ký\n- Xác thực email\n- Tạo tài khoản mới"),
        ("Đặt lịch khám với bác sĩ", "Là một người dùng, tôi muốn chọn ngày giờ và đặt lịch khám với bác sĩ, nhờ đó tôi có thể đảm bảo có lịch hẹn phù hợp", "An Nguyễn", 8, "Epic: Đặt lịch khám", "- Chọn bác sĩ và thời gian\n- Nhập thông tin khám\n- Xác nhận đặt lịch"),
        ("Xem thông tin chi tiết bác sĩ", "Là một người dùng, tôi muốn xem thông tin chi tiết bác sĩ để đưa ra quyết định đặt lịch", "Minh Trí", 4, "Epic: Quản lý bác sĩ", "- Hồ sơ chi tiết bác sĩ\n- Kinh nghiệm và chuyên môn\n- Đánh giá từ bệnh nhân"),
        ("Quản lý lịch làm việc bác sĩ", "Là một bác sĩ, tôi muốn thiết lập lịch làm việc để bệnh nhân có thể đặt lịch phù hợp", "Tong Nguyen", 7, "Epic: Quản lý bác sĩ", "- Thiết lập khung giờ làm việc\n- Quản lý ngày nghỉ\n- Cập nhật lịch trình"),
        
        # Sprint 3: Booking (5 stories)
        ("Hủy lịch hẹn", "Là một người dùng, tôi muốn hủy lịch hẹn, nhờ đó tôi có thể điều chỉnh khi có thay đổi kế hoạch", "An Nguyễn", 4, "Epic: Đặt lịch khám", "- Xem danh sách lịch hẹn\n- Chọn hủy lịch\n- Thông báo cho bác sĩ"),
        ("Thay đổi lịch hẹn", "Là một người dùng, tôi muốn thay đổi lịch hẹn, nhờ đó tôi có thể điều chỉnh khi có thay đổi kế hoạch", "Minh Trí", 5, "Epic: Đặt lịch khám", "- Chọn lịch hẹn cần thay đổi\n- Chọn thời gian mới\n- Xác nhận thay đổi"),
        ("Xác nhận lịch hẹn", "Là một bác sĩ, tôi muốn xác nhận lịch hẹn, nhờ đó tôi có thể quản lý thời gian linh hoạt", "Tong Nguyen", 3, "Epic: Quản lý bác sĩ", "- Xem yêu cầu đặt lịch\n- Xác nhận hoặc từ chối\n- Thông báo cho bệnh nhân"),
        ("Từ chối lịch hẹn", "Là một bác sĩ, tôi muốn từ chối lịch hẹn, nhờ đó tôi có thể quản lý thời gian linh hoạt", "An Nguyễn", 3, "Epic: Quản lý bác sĩ", "- Xem yêu cầu đặt lịch\n- Từ chối với lý do\n- Thông báo cho bệnh nhân"),
        ("Cập nhật trạng thái lịch hẹn", "Là một bác sĩ, tôi muốn cập nhật trạng thái lịch hẹn để theo dõi tiến trình", "Minh Trí", 4, "Epic: Quản lý bác sĩ", "- Đánh dấu trạng thái cuộc hẹn\n- Ghi chú khám bệnh\n- Lưu kết quả khám"),
        
        # Sprint 4: Reports (5 stories)
        ("Xem danh sách lịch hẹn", "Là một bác sĩ, tôi muốn xem danh sách lịch hẹn của mình, nhờ đó tôi có thể sắp xếp thời gian hiệu quả", "Tong Nguyen", 5, "Epic: Quản lý bác sĩ", "- Hiển thị lịch hẹn theo ngày\n- Thông tin bệnh nhân\n- Trạng thái cuộc hẹn"),
        ("Cập nhật thông tin cá nhân", "Là một người dùng, tôi muốn cập nhật thông tin cá nhân, nhờ đó tôi có thể giữ cho hồ sơ cá nhân luôn chính xác", "An Nguyễn", 4, "Epic: Quản lý bệnh nhân", "- Form cập nhật thông tin\n- Xác thực thay đổi\n- Lưu thông tin mới"),
        ("Xem lịch sử khám bệnh", "Là một người dùng, tôi muốn xem lịch sử khám bệnh để theo dõi sức khỏe", "Minh Trí", 6, "Epic: Quản lý bệnh nhân", "- Hiển thị lịch sử khám\n- Chi tiết từng lần khám\n- Kết quả và chẩn đoán"),
        ("Quản lý hồ sơ bệnh nhân", "Là một admin, tôi muốn quản lý hồ sơ bệnh nhân để đảm bảo dữ liệu chính xác", "Tong Nguyen", 7, "Epic: Trang quản trị", "- Xem danh sách bệnh nhân\n- Chỉnh sửa thông tin\n- Quản lý quyền truy cập"),
        ("Báo cáo thống kê hệ thống", "Là một admin, tôi muốn xem báo cáo thống kê để theo dõi hoạt động hệ thống", "An Nguyễn", 5, "Epic: Trang quản trị", "- Dashboard tổng quan\n- Báo cáo đặt lịch\n- Thống kê theo thời gian"),
    ]
    
    for summary, description, assignee, points, epic_name, criteria in story_data:
        epic_key = epics.get(epic_name)
        story_key = create_story(summary, description, assignee, points, epic_key, criteria)
        if story_key:
            stories.append({"key": story_key, "assignee": assignee, "points": points})
        time.sleep(0.5)
    
    # 3. Tạo Sprints
    print(f"\n🏃 Creating Sprints...")
    board_id = get_board_id()
    
    if not board_id:
        print(f"❌ Could not get board ID")
        return
    
    sprint_configs = [
        {"name": "Sprint 1: Doctors", "stories": stories[:2]},
        {"name": "Sprint 2: Auth", "stories": stories[2:7]},
        {"name": "Sprint 3: Booking", "stories": stories[7:12]},
        {"name": "Sprint 4: Reports", "stories": stories[12:]},
    ]
    
    for config in sprint_configs:
        sprint_id = create_sprint(config["name"], board_id)
        if sprint_id:
            story_keys = [story["key"] for story in config["stories"]]
            assign_stories_to_sprint(sprint_id, story_keys)
            
            # Summary
            total_points = sum(story["points"] for story in config["stories"])
            print(f"   📊 {config['name']}: {len(story_keys)} stories, {total_points} points")
        
        time.sleep(1)
    
    print(f"\n🎉 Simple setup completed!")
    print(f"📋 Created: 4 Epics, {len(stories)} Stories, 4 Sprints")
    print(f"🔗 View in Jira: {JIRA_URL}/jira/software/projects/{PROJECT_KEY}/boards")

if __name__ == "__main__":
    setup_simple_project()
