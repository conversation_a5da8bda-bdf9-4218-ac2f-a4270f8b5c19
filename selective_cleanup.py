#!/usr/bin/env python3
"""
Selectively remove duplicate commits only from specific author
"""
import subprocess
import sys
from collections import defaultdict

def run_git_command(cmd):
    """Run a git command and return the output"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.returncode != 0:
            print(f"Error running command: {cmd}")
            print(f"Error: {result.stderr}")
            return None
        return result.stdout.strip()
    except Exception as e:
        print(f"Exception running command: {cmd}")
        print(f"Exception: {e}")
        return None

def get_commit_details():
    """Get detailed commit information"""
    cmd = 'git log --pretty=format:"%H|%an|%ae|%ad|%s" --date=iso'
    output = run_git_command(cmd)
    if not output:
        return []
    
    commits = []
    for line in output.split('\n'):
        if '|' in line:
            parts = line.split('|', 4)
            if len(parts) == 5:
                commits.append({
                    'hash': parts[0],
                    'author': parts[1],
                    'email': parts[2], 
                    'date': parts[3],
                    'message': parts[4]
                })
    
    return commits

def find_author_duplicates(target_author="TongNguyenvk"):
    """Find duplicate commits only from specific author"""
    commits = get_commit_details()
    
    # Group commits by author and message
    author_message_groups = defaultdict(list)
    
    for commit in commits:
        if commit['author'] == target_author:
            key = f"{commit['author']}|{commit['message']}"
            author_message_groups[key].append(commit)
    
    # Only return groups with duplicates
    duplicates = {}
    for key, commits_list in author_message_groups.items():
        if len(commits_list) > 1:
            author, message = key.split('|', 1)
            duplicates[message] = commits_list
    
    return duplicates

def analyze_author_duplicates(target_author="TongNguyenvk"):
    """Analyze duplicate commits from specific author"""
    duplicates = find_author_duplicates(target_author)
    
    if not duplicates:
        print(f"No duplicate commits found for author: {target_author}")
        return
    
    print(f"=== DUPLICATE COMMITS ANALYSIS FOR {target_author} ===")
    total_duplicates = 0
    
    for message, commits in sorted(duplicates.items(), key=lambda x: len(x[1]), reverse=True):
        count = len(commits)
        total_duplicates += count - 1  # Keep one, remove others
        print(f"\nMessage: '{message}'")
        print(f"Count: {count} commits by {target_author}")
        print("Commits (newest to oldest):")
        for commit in commits:
            print(f"  - {commit['hash'][:8]} on {commit['date'][:10]}")
    
    print(f"\n=== SUMMARY ===")
    print(f"Duplicate message groups from {target_author}: {len(duplicates)}")
    print(f"Total commits that can be removed: {total_duplicates}")
    
    return duplicates

def selective_squash_duplicates(target_author="TongNguyenvk", message_pattern=None):
    """Squash duplicate commits from specific author with specific message pattern"""
    duplicates = find_author_duplicates(target_author)
    
    if not duplicates:
        print(f"No duplicate commits found for author: {target_author}")
        return False
    
    # Filter by message pattern if provided
    if message_pattern:
        filtered_duplicates = {}
        for message, commits in duplicates.items():
            if message_pattern.lower() in message.lower():
                filtered_duplicates[message] = commits
        duplicates = filtered_duplicates
    
    if not duplicates:
        print(f"No duplicate commits found matching pattern: {message_pattern}")
        return False
    
    print(f"=== SELECTIVE SQUASH FOR {target_author} ===")
    
    # Create backup
    backup_branch = f"backup-before-selective-cleanup-{target_author.lower()}"
    result = run_git_command(f'git branch {backup_branch}')
    if result is not None:
        print(f"Created backup branch: {backup_branch}")
    
    # Process each duplicate group
    for message, commits in duplicates.items():
        if len(commits) <= 1:
            continue
            
        print(f"\n--- Processing: '{message}' ---")
        print(f"Found {len(commits)} duplicate commits")
        
        # Sort by date (newest first)
        commits_sorted = sorted(commits, key=lambda x: x['date'], reverse=True)
        
        # Keep the newest, identify others to remove
        keep_commit = commits_sorted[0]
        remove_commits = commits_sorted[1:]
        
        print(f"KEEP:   {keep_commit['hash'][:8]} ({keep_commit['date'][:10]})")
        for commit in remove_commits:
            print(f"REMOVE: {commit['hash'][:8]} ({commit['date'][:10]})")
        
        # Ask for confirmation
        confirm = input(f"Squash these {len(remove_commits)} commits? (y/N): ").strip().lower()
        if confirm != 'y':
            print("Skipped")
            continue
        
        # Find positions of commits to squash
        all_commits_cmd = 'git log --oneline'
        all_commits_output = run_git_command(all_commits_cmd)
        if not all_commits_output:
            print("Could not get commit list")
            continue
            
        all_commits_lines = all_commits_output.split('\n')
        
        # Find the range to squash
        positions = []
        for i, line in enumerate(all_commits_lines):
            commit_hash = line.split()[0]
            for commit in commits_sorted:
                if line.startswith(commit['hash'][:7]):
                    positions.append(i)
                    break
        
        if len(positions) != len(commits_sorted):
            print(f"Could not find all commit positions. Found {len(positions)}, expected {len(commits_sorted)}")
            continue
        
        positions.sort()
        start_pos = positions[0]
        end_pos = positions[-1]
        
        # Calculate reset count
        reset_count = end_pos + 1
        squash_count = end_pos - start_pos + 1
        
        print(f"Will reset {reset_count} commits and squash {squash_count} of them")
        
        # Perform the squash
        result = run_git_command(f'git reset --soft HEAD~{reset_count}')
        if result is None:
            print("Failed to reset commits")
            continue
        
        # Create new commit with better message
        clean_message = message.replace('"', '\\"')
        if "deploy" in message.lower():
            new_message = f'ci: update deployment configuration\n\n- {clean_message}\n- Consolidated {len(commits)} duplicate commits\n- Improved deployment workflow'
        elif "gitworkflow" in message.lower():
            new_message = f'ci: add GitHub Actions workflow\n\n- {clean_message}\n- Consolidated {len(commits)} duplicate commits\n- Enhanced CI/CD pipeline'
        else:
            new_message = f'{clean_message}\n\nConsolidated {len(commits)} duplicate commits by {target_author}'
        
        result = run_git_command(f'git commit -m "{new_message}"')
        if result is None:
            print("Failed to create consolidated commit")
            # Try to restore
            run_git_command(f'git reset --hard {backup_branch}')
            continue
        
        print(f"✅ Successfully consolidated {len(commits)} commits")
        
        # Only process one group at a time to avoid conflicts
        break
    
    return True

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "analyze":
            author = sys.argv[2] if len(sys.argv) > 2 else "TongNguyenvk"
            analyze_author_duplicates(author)
        elif sys.argv[1] == "squash":
            author = sys.argv[2] if len(sys.argv) > 2 else "TongNguyenvk"
            pattern = sys.argv[3] if len(sys.argv) > 3 else None
            selective_squash_duplicates(author, pattern)
        else:
            print("Usage: python selective_cleanup.py [analyze|squash] [author] [pattern]")
    else:
        analyze_author_duplicates("TongNguyenvk")
