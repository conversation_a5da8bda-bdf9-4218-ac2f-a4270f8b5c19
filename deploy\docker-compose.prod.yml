version: '3.8'

services:
  frontend:
    image: tongnguyen/frontend:latest
    ports:
      - "${FRONTEND_PORT:-80}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - API_URL=http://backend:8080/api/
    depends_on:
      - backend
    networks:
      - app-network
    restart: unless-stopped

  backend:
    image: tongnguyen/backend:latest
    ports:
      - "${BACKEND_PORT:-8080}:8080"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=8080
      - DB_HOST=${DB_HOST:-db-mysql}
      - DB_PORT=${DB_PORT:-3306}
      - DB_NAME=${DB_NAME:-DBDKKHAMBENH}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD}
      - DATABASE_URL=mysql://${DB_USER:-root}:${DB_PASSWORD}@${DB_HOST:-db-mysql}:${DB_PORT:-3306}/${DB_NAME:-DBDKKHAMBENH}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
    depends_on:
      - db-mysql
    networks:
      - app-network
    restart: unless-stopped

  db-mysql:
    image: mysql:8.0
    restart: always
    environment:
      - MYSQL_DATABASE=${DB_NAME:-DBDKKHAMBENH}
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
    ports:
      - "${DB_EXTERNAL_PORT:-3306}:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - app-network

volumes:
  db_data:

networks:
  app-network:
    driver: bridge
