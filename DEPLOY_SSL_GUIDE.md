# 🚀 Hướng dẫn Triển khai SSL cho a2t.io.vn

## 📋 Tổng quan

Hướng dẫn này sẽ giúp bạn:
1. <PERSON><PERSON><PERSON> hình domain `a2t.io.vn` truy cập vào backend
2. Cài đặt SSL certificate miễn phí với Let's Encrypt
3. Thi<PERSON>t lập Nginx reverse proxy
4. <PERSON><PERSON><PERSON> hình HTTPS cho toàn bộ hệ thống

## 🔧 Bước 1: Chuẩn bị Server

### 1.1. Cập nhật hệ thống
```bash
sudo apt update && sudo apt upgrade -y
```

### 1.2. <PERSON><PERSON><PERSON> bảo domain đã trỏ đúng IP
Kiểm tra DNS:
```bash
nslookup a2t.io.vn
dig a2t.io.vn
```

## 🔒 Bước 2: Cài đặt SSL Certificate

### 2.1. Chạy script tự động
```bash
# Cấp quyền thực thi
chmod +x scripts/setup-ssl.sh

# Chạy script (cần quyền root)
sudo ./scripts/setup-ssl.sh
```

### 2.2. Hoặc cài đặt thủ công

**Cài đặt Nginx:**
```bash
sudo apt install nginx -y
sudo systemctl enable nginx
sudo systemctl start nginx
```

**Cài đặt Certbot:**
```bash
sudo apt install snapd -y
sudo snap install core; sudo snap refresh core
sudo snap install --classic certbot
sudo ln -s /snap/bin/certbot /usr/bin/certbot
```

**Lấy SSL certificate:**
```bash
sudo certbot --nginx -d a2t.io.vn -d www.a2t.io.vn
```

## ⚙️ Bước 3: Cấu hình Nginx

Nginx sẽ được cấu hình tự động bởi script, nhưng bạn có thể kiểm tra:

```bash
sudo nano /etc/nginx/sites-available/a2t.io.vn
```

Cấu hình sẽ bao gồm:
- Redirect HTTP → HTTPS
- Proxy frontend (port 3000)
- Proxy backend API (port 8080)
- Proxy uploads và API docs

## 🐳 Bước 4: Triển khai với Docker

### 4.1. Cập nhật biến môi trường
```bash
# Tạo file .env cho production
cat > .env << EOF
NODE_ENV=production
FRONTEND_PORT=3000
BACKEND_PORT=8080
NEXT_PUBLIC_API_URL=https://a2t.io.vn/api
DB_HOST=localhost
DB_NAME=cnpm_hospital_booking
DB_USER=root
DB_PASSWORD=your_mysql_password
EOF
```

### 4.2. Build và chạy containers
```bash
# Build images
docker-compose build

# Chạy services
docker-compose up -d

# Kiểm tra status
docker-compose ps
```

## 🔍 Bước 5: Kiểm tra và Xác minh

### 5.1. Kiểm tra SSL
```bash
# Test SSL certificate
curl -I https://a2t.io.vn

# Kiểm tra SSL rating
# Truy cập: https://www.ssllabs.com/ssltest/analyze.html?d=a2t.io.vn
```

### 5.2. Kiểm tra các endpoints
```bash
# Frontend
curl https://a2t.io.vn

# Backend API
curl https://a2t.io.vn/api/health

# API Documentation
curl https://a2t.io.vn/api-docs
```

### 5.3. Kiểm tra logs
```bash
# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Docker logs
docker-compose logs -f frontend
docker-compose logs -f backend
```

## 🔄 Bước 6: Tự động gia hạn SSL

SSL certificate sẽ tự động gia hạn, nhưng bạn có thể test:

```bash
# Test renewal
sudo certbot renew --dry-run

# Kiểm tra timer
sudo systemctl status snap.certbot.renew.timer
```

## 🛠️ Troubleshooting

### Lỗi CORS
Nếu gặp lỗi CORS, kiểm tra:
1. Backend đã cập nhật `ALLOWED_ORIGINS`
2. Frontend đã cấu hình đúng `NEXT_PUBLIC_API_URL`

### Lỗi SSL
```bash
# Kiểm tra certificate
sudo certbot certificates

# Renew manually nếu cần
sudo certbot renew --force-renewal
```

### Lỗi Nginx
```bash
# Test cấu hình
sudo nginx -t

# Reload cấu hình
sudo systemctl reload nginx
```

## 📊 Kết quả Mong đợi

Sau khi hoàn thành:

✅ **Frontend**: https://a2t.io.vn  
✅ **Backend API**: https://a2t.io.vn/api  
✅ **API Docs**: https://a2t.io.vn/api-docs  
✅ **SSL Certificate**: A+ rating  
✅ **Auto-renewal**: Enabled  

## 🔐 Bảo mật Bổ sung

### Security Headers
Nginx đã được cấu hình với các security headers:
- X-Frame-Options
- X-XSS-Protection  
- X-Content-Type-Options
- Referrer-Policy
- Content-Security-Policy

### Firewall
```bash
# Cấu hình UFW
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs Nginx và Docker
2. Verify DNS settings
3. Test SSL certificate
4. Check firewall rules

---

**Lưu ý**: Thay đổi `<EMAIL>` thành email thực của bạn trong script setup-ssl.sh
