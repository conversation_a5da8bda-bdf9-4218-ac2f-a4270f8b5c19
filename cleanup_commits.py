#!/usr/bin/env python3
"""
Script to help clean up duplicate commits to reduce Gini index
"""
import subprocess
import sys
from collections import defaultdict
import re


def run_git_command(cmd):
    """Run a git command and return the output"""
    try:
        result = subprocess.run(
            cmd, shell=True, capture_output=True, text=True, encoding="utf-8"
        )
        if result.returncode != 0:
            print(f"Error running command: {cmd}")
            print(f"Error: {result.stderr}")
            return None
        return result.stdout.strip()
    except Exception as e:
        print(f"Exception running command: {cmd}")
        print(f"Exception: {e}")
        return None


def get_commit_info():
    """Get all commits with their hash and message"""
    cmd = 'git log --pretty=format:"%H|%s"'
    output = run_git_command(cmd)
    if not output:
        return []

    commits = []
    for line in output.split("\n"):
        if "|" in line:
            hash_part, message = line.split("|", 1)
            commits.append({"hash": hash_part, "message": message})

    return commits


def find_duplicate_commits():
    """Find commits with duplicate messages"""
    commits = get_commit_info()
    message_groups = defaultdict(list)

    for commit in commits:
        message_groups[commit["message"]].append(commit)

    # Only return groups with more than 1 commit
    duplicates = {
        msg: commits for msg, commits in message_groups.items() if len(commits) > 1
    }
    return duplicates


def create_squash_plan():
    """Create a plan to squash duplicate commits"""
    duplicates = find_duplicate_commits()

    print("=== DUPLICATE COMMITS ANALYSIS ===")
    total_duplicates = 0

    for message, commits in sorted(
        duplicates.items(), key=lambda x: len(x[1]), reverse=True
    ):
        count = len(commits)
        total_duplicates += count - 1  # Keep one, remove others
        print(f"\nMessage: '{message}'")
        print(f"Count: {count} commits")
        print("Commits:")
        for commit in commits:
            print(f"  - {commit['hash'][:8]}")

    print(f"\n=== SUMMARY ===")
    print(f"Total duplicate commit messages: {len(duplicates)}")
    print(f"Total commits that can be removed: {total_duplicates}")

    return duplicates


def squash_duplicates_interactive():
    """Interactive squashing of duplicate commits"""
    duplicates = find_duplicate_commits()

    print("=== INTERACTIVE COMMIT SQUASHING ===")

    # Sort by count (most duplicates first)
    sorted_duplicates = sorted(
        duplicates.items(), key=lambda x: len(x[1]), reverse=True
    )

    for message, commits in sorted_duplicates:
        count = len(commits)
        if count < 2:
            continue

        print(f"\n--- Processing: '{message}' ({count} commits) ---")

        # Show commits in chronological order (oldest first)
        commits_sorted = sorted(commits, key=lambda x: x["hash"])

        print("Commits (oldest to newest):")
        for i, commit in enumerate(commits_sorted):
            print(f"  {i+1}. {commit['hash'][:8]} - {commit['message']}")

        # Ask user what to do
        print(f"\nOptions:")
        print(f"1. Keep oldest commit, squash others into it")
        print(f"2. Keep newest commit, squash others into it")
        print(f"3. Skip this group")
        print(f"4. Quit")

        choice = input("Choose option (1-4): ").strip()

        if choice == "1":
            # Keep oldest, squash others
            keep_commit = commits_sorted[0]
            squash_commits = commits_sorted[1:]
            print(f"Will keep: {keep_commit['hash'][:8]}")
            print(f"Will squash: {[c['hash'][:8] for c in squash_commits]}")

        elif choice == "2":
            # Keep newest, squash others
            keep_commit = commits_sorted[-1]
            squash_commits = commits_sorted[:-1]
            print(f"Will keep: {keep_commit['hash'][:8]}")
            print(f"Will squash: {[c['hash'][:8] for c in squash_commits]}")

        elif choice == "3":
            print("Skipping this group...")
            continue

        elif choice == "4":
            print("Exiting...")
            break

        else:
            print("Invalid choice, skipping...")
            continue


def create_rebase_script():
    """Create a script to perform the actual squashing"""
    duplicates = find_duplicate_commits()

    print("=== CREATING AUTOMATED SQUASH SCRIPT ===")

    # We'll use git filter-repo to remove duplicate commits
    # Keep the newest commit of each duplicate group
    commits_to_remove = []

    for message, commits in duplicates.items():
        if len(commits) < 2:
            continue

        # Sort by commit order (newest first in git log)
        commits_sorted = commits  # Already in git log order (newest first)

        # Keep the first (newest), remove the rest
        keep_commit = commits_sorted[0]
        remove_commits = commits_sorted[1:]

        print(f"\nMessage: '{message}'")
        print(f"  Keep: {keep_commit['hash'][:8]} (newest)")
        print(f"  Remove: {[c['hash'][:8] for c in remove_commits]}")

        commits_to_remove.extend([c["hash"] for c in remove_commits])

    print(f"\nTotal commits to remove: {len(commits_to_remove)}")

    # Create the filter-repo command
    if commits_to_remove:
        with open("remove_commits.txt", "w") as f:
            for commit_hash in commits_to_remove:
                f.write(f"{commit_hash}\n")

        print("\nCreated 'remove_commits.txt' with commits to remove")
        print("\nTo execute the cleanup, run:")
        print(
            "git filter-repo --commit-callback 'return commit.original_id not in open(\"remove_commits.txt\").read().split()'"
        )
        print("\nWARNING: This will rewrite git history. Make sure you have a backup!")


def auto_squash_remaining():
    """Automatically squash remaining duplicate commits"""
    duplicates = find_duplicate_commits()

    print("=== AUTO SQUASHING REMAINING DUPLICATES ===")

    # Sort by count (most duplicates first)
    sorted_duplicates = sorted(
        duplicates.items(), key=lambda x: len(x[1]), reverse=True
    )

    total_removed = 0

    for message, commits in sorted_duplicates:
        count = len(commits)
        if count < 2:
            continue

        print(f"\n--- Processing: '{message}' ({count} commits) ---")

        # Find position of these commits in current git log
        cmd = f'git log --oneline --grep="{message}" --fixed-strings'
        output = run_git_command(cmd)

        if not output:
            print(f"  No commits found for message: {message}")
            continue

        matching_commits = output.split("\n")
        if len(matching_commits) != count:
            print(f"  Warning: Expected {count} commits, found {len(matching_commits)}")
            continue

        print(f"  Found {len(matching_commits)} commits to squash")

        # Get the position of the oldest commit
        oldest_commit_line = matching_commits[-1]
        oldest_hash = oldest_commit_line.split()[0]

        # Find position in git log
        cmd = "git log --oneline"
        all_commits = run_git_command(cmd).split("\n")

        position = -1
        for i, commit_line in enumerate(all_commits):
            if commit_line.startswith(oldest_hash):
                position = i
                break

        if position == -1:
            print(f"  Could not find position of commit {oldest_hash}")
            continue

        # Calculate how many commits to reset
        reset_count = position + 1

        print(f"  Resetting {reset_count} commits and squashing {count} duplicates")

        # Perform the squash
        cmd = f"git reset --soft HEAD~{reset_count}"
        result = run_git_command(cmd)
        if result is None:
            print(f"  Error resetting commits")
            continue

        # Create consolidated commit message
        clean_message = message.replace('"', '\\"')
        cmd = f'git commit -m "{clean_message} (consolidated)"'
        result = run_git_command(cmd)
        if result is None:
            print(f"  Error creating consolidated commit")
            continue

        print(f"  ✓ Successfully squashed {count} commits")
        total_removed += count - 1

        # Break after first successful squash to avoid conflicts
        break

    print(f"\nTotal commits removed in this run: {total_removed}")
    return total_removed


def calculate_gini_index():
    """Calculate Gini index for commit message distribution"""
    commits = get_commit_info()

    if not commits:
        return 0.0

    # Count frequency of each commit message
    message_counts = {}
    for commit in commits:
        message = commit["message"]
        message_counts[message] = message_counts.get(message, 0) + 1

    # Get frequencies
    frequencies = list(message_counts.values())
    n = len(frequencies)
    total_commits = sum(frequencies)

    if n <= 1:
        return 0.0

    # Sort frequencies
    frequencies.sort()

    # Calculate Gini coefficient
    index = 0
    for i in range(n):
        index += (2 * (i + 1) - n - 1) * frequencies[i]

    gini = index / (n * total_commits)

    print(f"=== GINI INDEX CALCULATION ===")
    print(f"Total commits: {total_commits}")
    print(f"Unique commit messages: {n}")
    print(f"Gini Index: {gini:.4f}")

    # Show distribution
    print(f"\n=== COMMIT MESSAGE DISTRIBUTION ===")
    sorted_messages = sorted(message_counts.items(), key=lambda x: x[1], reverse=True)
    for message, count in sorted_messages[:10]:  # Top 10
        percentage = (count / total_commits) * 100
        print(
            f"{count:3d} ({percentage:5.1f}%) - {message[:60]}{'...' if len(message) > 60 else ''}"
        )

    if len(sorted_messages) > 10:
        print(f"... and {len(sorted_messages) - 10} more unique messages")

    return gini


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        squash_duplicates_interactive()
    elif len(sys.argv) > 1 and sys.argv[1] == "script":
        create_rebase_script()
    elif len(sys.argv) > 1 and sys.argv[1] == "auto":
        auto_squash_remaining()
    elif len(sys.argv) > 1 and sys.argv[1] == "gini":
        calculate_gini_index()
    else:
        create_squash_plan()
