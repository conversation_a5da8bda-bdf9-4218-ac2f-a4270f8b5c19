# 🏥 Website Đăng Ký Lịch Khám Bệnh

Ứng dụng web cho phép bệnh nhân đặt lịch khám bệnh trực tuyến.

## M<PERSON><PERSON> lục

- [🏥 Website Đăng Ký Lịch Khám Bệnh](#-website-đăng-ký-lịch-khám-bệnh)
  - [<PERSON><PERSON><PERSON> lục](#mục-lục)
  - [<PERSON><PERSON> tả](#mô-tả)
  - [Yêu cầu](#yêu-cầu)
  - [Cài đặt](#cài-đặt)
    - [Cài đặt Local](#cài-đặt-local)
    - [Cài đặt Docker](#cài-đặt-docker)
  - [Cấu hình](#cấu-hình)
    - [Biến môi trường](#biến-môi-trường)
    - [Cấu hình Cơ Sở Dữ Liệu](#cấu-hình-cơ-sở-dữ-liệu)
  - [Chạy Ứng Dụng](#chạy-ứng-dụng)
    - [Chạy Local](#chạy-local)
    - [<PERSON><PERSON><PERSON> Docker](#chạy-docker)
  - [C<PERSON>u trúc thư mục](#cấu-trúc-thư-mục)
  - [Chạy Ứng Dụng](#chạy-ứng-dụng-1)
    - [Chạy Local](#chạy-local-1)
    - [Chạy Docker](#chạy-docker-1)
  - [Cấu trúc thư mục](#cấu-trúc-thư-mục-1)
  - [Đóng góp](#đóng-góp)

## Mô tả

Ứng dụng này cho phép bệnh nhân xem thông tin bác sĩ, chuyên khoa, lịch khám và đặt lịch hẹn trực tuyến. Bác sĩ có thể quản lý lịch hẹn của mình. Quản trị viên có thể quản lý người dùng, chuyên khoa và các cài đặt khác.

## Yêu cầu

- [Node.js](https://nodejs.org/) (phiên bản 20.x)
- [npm](https://www.npmjs.com/) (hoặc [yarn](https://yarnpkg.com/))
- [MySQL](https://www.mysql.com/) (hoặc [PostgreSQL](https://www.postgresql.org/))
- [Docker](https://www.docker.com/) (tùy chọn)
- [Docker Compose](https://docs.docker.com/compose/install/) (tùy chọn)

## Cài đặt

### Cài đặt Local

1.  Clone kho lưu trữ:

    ```bash
    git clone https://github.com/your-username/your-repository.git
    cd your-repository
    ```

2.  Cài đặt các dependencies cho frontend:

    ```bash
    cd frontend
    npm install
    ```

3.  Cài đặt các dependencies cho backend:

    ```bash
    cd ../backend
    npm install
    ```

### Cài đặt Docker

1.  Đảm bảo bạn đã cài đặt [Docker](https://www.docker.com/) và [Docker Compose](https://docs.docker.com/compose/install/).

## Cấu hình

### Biến môi trường

1.  Tạo file `.env` trong thư mục `backend` và thiết lập các biến môi trường cần thiết. Bạn có thể sao chép từ file `.env.example` (nếu có) và thay đổi các giá trị cho phù hợp với môi trường của bạn.

    ```
    PORT=8080
    DATABASE_URL=mysql://root:your_mysql_password@localhost:3306/your_database_name
    JWT_SECRET=your_jwt_secret_key
    # ... các biến môi trường khác
    ```

    **Quan trọng:** Không commit file `.env` lên kho lưu trữ.

### Cấu hình Cơ Sở Dữ Liệu

1.  Tạo một cơ sở dữ liệu MySQL (hoặc PostgreSQL) với tên `your_database_name`.
2.  Cấu hình thông tin kết nối cơ sở dữ liệu trong file `backend/config/config.json` hoặc thông qua biến môi trường `DATABASE_URL`.

    Ví dụ (`config/config.json`):

    ```json
    {
      "development": {
        "username": "your_username",
        "password": "your_password",
        "database": "your_database_name",
        "host": "127.0.0.1",
        "dialect": "mysql"
      },
      "test": {
        // ...
      },
      "production": {
        // ...
      }
    }
    ```

## Chạy Ứng Dụng

### Chạy Local

1.  Chạy backend:

    ```bash
    cd backend
    npm run dev
    ```

2.  Chạy frontend:

    ```bash
    cd frontend
    npm run dev
    ```

    Truy cập ứng dụng frontend tại `http://localhost:3000`.

### Chạy Docker

1.  Chạy lệnh sau trong thư mục gốc của dự án:

    ```bash
    docker-compose up --build
    ```

    Lệnh này sẽ build các Docker images và khởi động các containers cho frontend, backend và cơ sở dữ liệu.

2.  Truy cập ứng dụng:

    *   Frontend: Truy cập `http://localhost:3000`.
    *   Backend: Truy cập `http://localhost:8080` để kiểm tra các API.

## Cấu trúc thư mục
<pre   },
      "production": {
        // ...
      }
    }
    ```

## Chạy Ứng Dụng

### Chạy Local

1.  Chạy backend:

    ```bash
    cd backend
    npm run dev
    ```

2.  Chạy frontend:

    ```bash
    cd frontend
    npm run dev
    ```

    Truy cập ứng dụng frontend tại `http://localhost:3000`.

### Chạy Docker

1.  Chạy lệnh sau trong thư mục gốc của dự án:

    ```bash
    docker-compose up --build
    ```

    Lệnh này sẽ build các Docker images và khởi động các containers cho frontend, backend và cơ sở dữ liệu.

2.  Truy cập ứng dụng:

    *   Frontend: Truy cập `http://localhost:3000`.
    *   Backend: Truy cập `http://localhost:8080` để kiểm tra các API.

## Cấu trúc thư mục
<pre>
your-repository/
├── .git/
├── .gitignore
├── docker-compose.yml
├── README.md
├── frontend/
│ ├── src/
│ ├── public/
│ ├── package.json
│ ├── next.config.js
│ └── Dockerfile
└── backend/
├── src/
├── config/
├── models/
├── migrations/
├── seeders/
├── package.json
├── server.js
└── Dockerfile
</pre>
## Đóng góp

Chúng tôi hoan nghênh mọi đóng góp cho dự án này. Vui lòng tạo một pull request với các thay đổi của bạn.
