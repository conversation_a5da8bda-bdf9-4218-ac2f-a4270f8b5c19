#!/usr/bin/env python3
"""
Remove duplicate "Update deploy.yml" commits while keeping the most recent one
"""
import subprocess
import sys

def run_git_command(cmd):
    """Run a git command and return the output"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.returncode != 0:
            print(f"Error running command: {cmd}")
            print(f"Error: {result.stderr}")
            return None
        return result.stdout.strip()
    except Exception as e:
        print(f"Exception running command: {cmd}")
        print(f"Exception: {e}")
        return None

def get_deploy_commits():
    """Get all 'Update deploy.yml' commits"""
    cmd = 'git log --oneline --grep="Update deploy.yml"'
    output = run_git_command(cmd)
    
    if not output:
        print("No 'Update deploy.yml' commits found")
        return []
    
    commits = []
    for line in output.split('\n'):
        if line.strip():
            hash_part = line.split()[0]
            commits.append(hash_part)
    
    return commits

def remove_duplicate_deploy_commits():
    """Remove duplicate deploy commits, keeping only the most recent one"""
    deploy_commits = get_deploy_commits()
    
    if len(deploy_commits) <= 1:
        print("Only one or no 'Update deploy.yml' commits found")
        return
    
    print(f"Found {len(deploy_commits)} 'Update deploy.yml' commits")
    print("Will keep the most recent one and remove the rest")
    
    # Keep the first one (most recent), remove the rest
    keep_commit = deploy_commits[0]
    remove_commits = deploy_commits[1:]
    
    print(f"KEEP:   {keep_commit}")
    print(f"REMOVE: {len(remove_commits)} commits")
    
    # Create backup
    backup_branch = "backup-before-deploy-cleanup"
    result = run_git_command(f'git branch {backup_branch}')
    if result is not None:
        print(f"Created backup branch: {backup_branch}")
    
    # Remove commits one by one using git rebase --onto
    success_count = 0
    
    for i, commit_hash in enumerate(remove_commits):
        print(f"\nRemoving commit {i+1}/{len(remove_commits)}: {commit_hash}")
        
        # Check if commit still exists
        result = run_git_command(f'git show --format="" --name-only {commit_hash}')
        if result is None:
            print(f"  Commit {commit_hash} not found, skipping")
            continue
        
        # Find the parent of this commit
        parent_result = run_git_command(f'git rev-parse {commit_hash}^')
        if parent_result is None:
            print(f"  Could not find parent of {commit_hash}")
            continue
        
        parent_hash = parent_result.strip()
        
        # Use git rebase --onto to remove this commit
        cmd = f'git rebase --onto {parent_hash} {commit_hash}'
        result = run_git_command(cmd)
        
        if result is not None:
            print(f"  ✅ Successfully removed {commit_hash}")
            success_count += 1
        else:
            print(f"  ❌ Failed to remove {commit_hash}")
            # Try to abort the rebase
            run_git_command('git rebase --abort')
            break
    
    print(f"\n=== CLEANUP COMPLETE ===")
    print(f"Successfully removed {success_count}/{len(remove_commits)} duplicate commits")
    
    # Show final state
    remaining_deploy_commits = get_deploy_commits()
    print(f"Remaining 'Update deploy.yml' commits: {len(remaining_deploy_commits)}")
    
    result = run_git_command('git rev-list --count HEAD')
    if result:
        print(f"Total commits now: {result}")

def interactive_remove():
    """Interactive removal with user confirmation"""
    deploy_commits = get_deploy_commits()
    
    if len(deploy_commits) <= 1:
        print("Only one or no 'Update deploy.yml' commits found")
        return
    
    print(f"Found {len(deploy_commits)} 'Update deploy.yml' commits:")
    
    # Show commits with more details
    for i, commit_hash in enumerate(deploy_commits):
        cmd = f'git show --format="%h %ad %s" --date=short --name-only {commit_hash}'
        result = run_git_command(cmd)
        if result:
            lines = result.split('\n')
            commit_info = lines[0] if lines else f"{commit_hash} (no info)"
            print(f"{i+1:2d}. {commit_info}")
    
    print(f"\nWill keep commit #1 (most recent) and remove the other {len(deploy_commits)-1} commits")
    
    confirm = input("Do you want to proceed? (y/N): ").strip().lower()
    if confirm != 'y':
        print("Cancelled")
        return
    
    remove_duplicate_deploy_commits()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        interactive_remove()
    else:
        remove_duplicate_deploy_commits()
