#!/usr/bin/env python3
"""
Smart Git History Cleanup - Remove duplicate commits while preserving natural history
"""
import subprocess
import sys
from collections import defaultdict
import tempfile
import os


def run_git_command(cmd):
    """Run a git command and return the output"""
    try:
        result = subprocess.run(
            cmd, shell=True, capture_output=True, text=True, encoding="utf-8"
        )
        if result.returncode != 0:
            print(f"Error running command: {cmd}")
            print(f"Error: {result.stderr}")
            return None
        return result.stdout.strip()
    except Exception as e:
        print(f"Exception running command: {cmd}")
        print(f"Exception: {e}")
        return None


def get_commit_details():
    """Get detailed commit information"""
    cmd = 'git log --pretty=format:"%H|%an|%ae|%ad|%s" --date=iso'
    output = run_git_command(cmd)
    if not output:
        return []

    commits = []
    for line in output.split("\n"):
        if "|" in line:
            parts = line.split("|", 4)
            if len(parts) == 5:
                commits.append(
                    {
                        "hash": parts[0],
                        "author": parts[1],
                        "email": parts[2],
                        "date": parts[3],
                        "message": parts[4],
                    }
                )

    return commits


def find_duplicate_groups():
    """Find groups of commits with identical messages"""
    commits = get_commit_details()
    message_groups = defaultdict(list)

    for commit in commits:
        message_groups[commit["message"]].append(commit)

    # Only return groups with duplicates
    duplicates = {
        msg: commits for msg, commits in message_groups.items() if len(commits) > 1
    }
    return duplicates


def create_filter_repo_script():
    """Create a git filter-repo script to remove specific commits"""
    duplicates = find_duplicate_groups()

    if not duplicates:
        print("No duplicate commits found!")
        return False

    print("=== DUPLICATE COMMIT ANALYSIS ===")
    commits_to_remove = []

    for message, commits in duplicates.items():
        print(f"\nMessage: '{message}'")
        print(f"Found {len(commits)} duplicate commits:")

        # Sort by date to keep the earliest (most natural)
        commits_sorted = sorted(commits, key=lambda x: x["date"])

        # Keep the first (earliest), remove the rest
        keep_commit = commits_sorted[0]
        remove_commits = commits_sorted[1:]

        print(
            f"  KEEP:   {keep_commit['hash'][:8]} by {keep_commit['author']} on {keep_commit['date'][:10]}"
        )

        for commit in remove_commits:
            print(
                f"  REMOVE: {commit['hash'][:8]} by {commit['author']} on {commit['date'][:10]}"
            )
            commits_to_remove.append(commit["hash"])

    print(f"\nTotal commits to remove: {len(commits_to_remove)}")

    if not commits_to_remove:
        return False

    # Create Python script for git filter-repo
    script_content = f"""#!/usr/bin/env python3
import git_filter_repo as fr

# List of commit hashes to remove
COMMITS_TO_REMOVE = {{
{chr(10).join(f'    b"{hash}",' for hash in commits_to_remove)}
}}

def commit_callback(commit):
    # Remove commits in our blacklist
    if commit.original_id in COMMITS_TO_REMOVE:
        return None  # Skip this commit
    return commit

# Set up the filter
args = fr.FilteringOptions.parse_args(['--force'])
filter = fr.RepoFilter(args, commit_callback=commit_callback)
filter.run()
"""

    with open("filter_repo_script.py", "w") as f:
        f.write(script_content)

    os.chmod("filter_repo_script.py", 0o755)

    print(f"\nCreated filter_repo_script.py")
    print(f"To execute the cleanup, run:")
    print(f"python filter_repo_script.py")

    return True


def simple_remove_duplicates():
    """Simple approach: use git rebase to remove duplicates"""
    duplicates = find_duplicate_groups()

    if not duplicates:
        print("No duplicate commits found!")
        return False

    print("=== SIMPLE DUPLICATE REMOVAL ===")
    commits_to_remove = set()

    for message, commits in duplicates.items():
        # Sort by date to keep the earliest
        commits_sorted = sorted(commits, key=lambda x: x["date"])

        # Remove all but the first (earliest)
        for commit in commits_sorted[1:]:
            commits_to_remove.add(commit["hash"])

    print(f"Will remove {len(commits_to_remove)} duplicate commits")

    # Use git filter-repo with commit exclusion
    with open("commits_to_remove.txt", "w") as f:
        for commit_hash in commits_to_remove:
            f.write(f"{commit_hash}\n")

    print(f"\nCreated commits_to_remove.txt")
    print(f"To execute the cleanup, run:")
    print(
        f"git filter-repo --commit-callback 'return None if commit.original_id.decode() in open(\"commits_to_remove.txt\").read().split() else commit' --force"
    )

    return True


def execute_smart_cleanup():
    """Execute the smart cleanup by removing duplicate commits one by one"""
    duplicates = find_duplicate_groups()

    if not duplicates:
        print("No duplicate commits found!")
        return False

    print("=== EXECUTING SMART CLEANUP ===")
    commits_to_remove = []

    for message, commits in duplicates.items():
        # Sort by date to keep the earliest
        commits_sorted = sorted(commits, key=lambda x: x["date"])

        # Remove all but the first (earliest)
        for commit in commits_sorted[1:]:
            commits_to_remove.append(commit["hash"])

    print(f"Will remove {len(commits_to_remove)} duplicate commits")

    # Remove commits one by one using git rebase
    total_removed = 0

    for i, commit_hash in enumerate(commits_to_remove):
        print(f"\nRemoving commit {i+1}/{len(commits_to_remove)}: {commit_hash[:8]}")

        # Check if commit still exists
        result = run_git_command(f'git show --format="" --name-only {commit_hash}')
        if result is None:
            print(f"  Commit {commit_hash[:8]} already removed or not found")
            continue

        # Use git rebase to remove this specific commit
        cmd = f"git rebase --onto {commit_hash}^ {commit_hash}"
        result = run_git_command(cmd)

        if result is not None:
            print(f"  ✅ Successfully removed {commit_hash[:8]}")
            total_removed += 1
        else:
            print(f"  ❌ Failed to remove {commit_hash[:8]}")
            # Try to abort the rebase
            run_git_command("git rebase --abort")

    print(f"\n=== CLEANUP COMPLETE ===")
    print(f"Successfully removed {total_removed} duplicate commits")

    # Show final state
    result = run_git_command("git rev-list --count HEAD")
    if result:
        print(f"Final commit count: {result}")

    return True


def cherry_pick_cleanup():
    """Use cherry-pick to create clean history"""
    duplicates = find_duplicate_groups()

    if not duplicates:
        print("No duplicate commits found!")
        return False

    print("=== CHERRY-PICK CLEANUP ===")

    # Get all commits in chronological order
    all_commits = get_commit_details()
    all_commits.reverse()  # Oldest first

    commits_to_keep = []
    commits_to_skip = set()

    # Identify commits to skip (duplicates except the earliest)
    for message, commits in duplicates.items():
        commits_sorted = sorted(commits, key=lambda x: x["date"])
        # Keep the first (earliest), skip the rest
        keep_commit = commits_sorted[0]
        skip_commits = commits_sorted[1:]

        print(f"\nMessage: '{message}'")
        print(f"  KEEP:   {keep_commit['hash'][:8]} ({keep_commit['date'][:10]})")

        for commit in skip_commits:
            print(f"  SKIP:   {commit['hash'][:8]} ({commit['date'][:10]})")
            commits_to_skip.add(commit["hash"])

    # Build list of commits to keep
    for commit in all_commits:
        if commit["hash"] not in commits_to_skip:
            commits_to_keep.append(commit)

    print(f"\n=== SUMMARY ===")
    print(f"Original commits: {len(all_commits)}")
    print(f"Commits to skip: {len(commits_to_skip)}")
    print(f"Commits to keep: {len(commits_to_keep)}")

    # Create new branch for clean history
    branch_name = "clean-history"

    print(f"\nCreating clean history branch: {branch_name}")

    # Create orphan branch
    result = run_git_command(f"git checkout --orphan {branch_name}")
    if result is None:
        print("Failed to create orphan branch")
        return False

    # Remove all files from staging
    run_git_command("git rm -rf .")

    # Cherry-pick commits in order
    success_count = 0

    for i, commit in enumerate(commits_to_keep):
        print(
            f"Cherry-picking {i+1}/{len(commits_to_keep)}: {commit['hash'][:8]} - {commit['message'][:50]}"
        )

        # Cherry-pick the commit
        result = run_git_command(f'git cherry-pick {commit["hash"]}')

        if result is not None:
            success_count += 1
        else:
            print(f"  ❌ Failed to cherry-pick {commit['hash'][:8]}")
            # Try to continue
            run_git_command("git cherry-pick --abort")

    print(f"\n=== CHERRY-PICK COMPLETE ===")
    print(f"Successfully cherry-picked {success_count}/{len(commits_to_keep)} commits")

    # Show final state
    result = run_git_command("git rev-list --count HEAD")
    if result:
        print(f"Clean history commit count: {result}")

    print(f"\nTo use the clean history:")
    print(f"1. git checkout main")
    print(f"2. git reset --hard {branch_name}")
    print(f"3. git branch -D {branch_name}")

    return True


def smart_rebase_cleanup():
    """Use interactive rebase to remove duplicates more naturally"""
    duplicates = find_duplicate_groups()

    if not duplicates:
        print("No duplicate commits found!")
        return

    print("=== SMART REBASE CLEANUP ===")

    # Get all commits in chronological order
    all_commits = get_commit_details()
    all_commits.reverse()  # Oldest first

    commits_to_remove = set()

    # Identify commits to remove (keep earliest of each duplicate group)
    for message, commits in duplicates.items():
        commits_sorted = sorted(commits, key=lambda x: x["date"])
        # Remove all but the first (earliest)
        for commit in commits_sorted[1:]:
            commits_to_remove.add(commit["hash"])

    print(f"Will remove {len(commits_to_remove)} duplicate commits")

    # Create rebase todo list
    todo_lines = []
    for commit in all_commits:
        if commit["hash"] in commits_to_remove:
            # Skip this commit
            continue
        else:
            # Keep this commit
            todo_lines.append(f"pick {commit['hash'][:8]} {commit['message']}")

    # Write to temporary file
    with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False) as f:
        f.write("\n".join(todo_lines))
        todo_file = f.name

    print(f"\nGenerated rebase todo list with {len(todo_lines)} commits")
    print(f"Removed {len(all_commits) - len(todo_lines)} duplicate commits")

    # Show what will be removed
    print(f"\n=== COMMITS TO BE REMOVED ===")
    for commit in all_commits:
        if commit["hash"] in commits_to_remove:
            print(
                f"  {commit['hash'][:8]} - {commit['message'][:60]} (by {commit['author']})"
            )

    print(f"\nTo execute the cleanup:")
    print(f"1. git rebase -i --root")
    print(f"2. Replace the todo list with the content from: {todo_file}")
    print(f"3. Save and exit")

    # Show the todo file content
    print(f"\n=== REBASE TODO CONTENT ===")
    with open(todo_file, "r") as f:
        content = f.read()
        print(content[:1000] + "..." if len(content) > 1000 else content)

    return todo_file


def restore_original_history():
    """Restore to original history before cleanup"""
    print("=== RESTORING ORIGINAL HISTORY ===")

    # Check if backup exists
    backup_branches = ["backup-before-cleanup", "backup-before-gini-cleanup"]

    for branch in backup_branches:
        result = run_git_command(f"git show-ref --verify --quiet refs/heads/{branch}")
        if result is not None:
            print(f"Found backup branch: {branch}")

            # Reset to backup
            result = run_git_command(f"git reset --hard {branch}")
            if result is not None:
                print(f"✅ Successfully restored to {branch}")

                # Show current state
                result = run_git_command("git log --oneline -10")
                if result:
                    print(f"\nCurrent commits (latest 10):")
                    print(result)

                return True
            else:
                print(f"❌ Failed to restore to {branch}")

    print("❌ No backup branches found!")
    return False


if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "analyze":
            duplicates = find_duplicate_groups()
            if duplicates:
                print(f"Found {len(duplicates)} groups of duplicate commits")
                for msg, commits in duplicates.items():
                    print(f"  '{msg}': {len(commits)} commits")
            else:
                print("No duplicate commits found")

        elif sys.argv[1] == "filter":
            create_filter_repo_script()

        elif sys.argv[1] == "simple":
            simple_remove_duplicates()

        elif sys.argv[1] == "execute":
            execute_smart_cleanup()

        elif sys.argv[1] == "cherry-pick":
            cherry_pick_cleanup()

        elif sys.argv[1] == "rebase":
            smart_rebase_cleanup()

        elif sys.argv[1] == "restore":
            restore_original_history()

        else:
            print("Usage: python smart_cleanup.py [analyze|filter|rebase|restore]")
    else:
        print("Usage: python smart_cleanup.py [analyze|filter|rebase|restore]")
        print("  analyze - Show duplicate commit analysis")
        print("  filter  - Create filter-branch script")
        print("  rebase  - Create smart rebase todo list")
        print("  restore - Restore original history from backup")
